package com.qinglusaas.connect.domain.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.*;
import java.math.BigDecimal;

import com.qinglusaas.connect.domain.service.impl.HelloStoreServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.InjectMocks;

import com.ql.dto.msg.PlatformSyncParam;
import com.ql.dto.ApiResultResp;
import com.ql.enums.open.ModificationBusiTypeEnum;

import com.qinglusaas.connect.infra.persistence.dao.ApiConnRepository;
import com.qinglusaas.connect.infra.persistence.dao.store.ThirdIdRelationRepository;
import com.qinglusaas.connect.infra.persistence.dao.ThirdPlatformSourceInfoRepository;
import com.qinglusaas.connect.infra.persistence.dao.vehicle.ThirdVehicleIdRelationRepository;
import com.qinglusaas.connect.infra.persistence.dao.vehicle.VehicleBindRepository;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.ThirdIdRelationEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.ThirdVehicleIdRelationEntity;

import com.qinglusaas.connect.infra.remote.saas.client.SaasClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasStoreClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasPriceClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasVehicleClient;
import com.qinglusaas.connect.infra.remote.saas.contants.IdRelationEnum;
import com.qinglusaas.connect.infra.remote.saas.dto.BusinessTimeDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.LongLatDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.RichStoreInfoDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.ServicePickupDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.StoreContactDTO;
import com.qinglusaas.connect.infra.remote.saas.dto.StoreInfoDTO;

import com.qinglusaas.connect.infra.remote.hello.client.HelloClientWrapper;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.ResultResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.ServiceCircleNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.StoreModifyNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.StoreOnlineNotifyResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.VehicleSettleResp;
import com.qinglusaas.connect.infra.remote.hello.vo.request.StoreInfoReq;
import com.qinglusaas.connect.infra.remote.hello.vo.request.StoreOnlineReq;
import com.qinglusaas.connect.infra.remote.hello.vo.request.CarOccupyInventoryQueryReq;
import com.qinglusaas.connect.infra.remote.hello.vo.request.HelloVehicleSettleReq;

import com.qinglusaas.connect.client.hello.constants.ErrorCode;
import com.qinglusaas.connect.client.saas.vo.response.SaasResultResp;
import com.qinglusaas.connect.client.saas.vo.request.VehicleSettleReq;
import com.qinglusaas.connect.client.common.error.ClientName;

import com.qinglusaas.connect.domain.service.impl.HelloBaseServiceImpl;
import com.qinglusaas.connect.domain.converter.hello.HelloSiteInfoConverter;

import com.qinglusaas.connect.infra.exception.BizException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.jboss.logging.Logger;

import com.qinglusaas.connect.infra.remote.hello.vo.reponse.QueryCarOccupyInventoryResp;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyList;

@ExtendWith(MockitoExtension.class)
public class IHelloBaseServiceTest {

    @InjectMocks
    private HelloBaseServiceImpl helloBaseService;

    @InjectMocks
    private HelloStoreServiceImpl helloStoreService;
    
    @Mock
    private IStoreService storeService;
    
    @Mock
    private ApiConnRepository apiConnRepository;
    
    @Mock
    private ThirdIdRelationRepository thirdIdRelationRepository;
    
    @Mock
    private SaasStoreClient saasStoreClient;
    
    @Mock
    private SaasClient saasClient;

    @Mock
    private SaasPriceClient saasPriceClient;

    @Mock
    private ThirdPlatformSourceInfoRepository thirdPlatformSourceInfoRepository;

    @Mock
    private HelloClientWrapper helloClientWrapper;

    @Mock
    private Logger logger;

    @Mock
    private HelloSiteInfoConverter helloSiteInfoConverter;

    @Mock
    private ThirdVehicleIdRelationRepository thirdVehicleIdRelationRepository;
    
    @Mock
    private SaasVehicleClient saasVehicleClient;
    
    @Mock
    private VehicleBindRepository vehicleBindRepository;

    private ObjectMapper objectMapper;

    // 测试数据常量
    private static final Long MERCHANT_ID = 123456L;
    private static final Long CHANNEL_ID = 4L;
    private static final Long STORE_ID = 123456L;
    private static final String THIRD_ID = "789";
    private static final Long SAAS_ID = 123456L;
    private static final String THIRD_MERCHANT_ID = "20000038603";
    private static final Long HELLO = 4L;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    private void setupApiConnRepository() {
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID)))
            .thenReturn(Optional.of(apiConnEntity));
    }

    private void setupThirdIdRelationRepository() {
        ThirdIdRelationEntity thirdIdRelationEntity = new ThirdIdRelationEntity();
        thirdIdRelationEntity.setThirdId("123");
        thirdIdRelationEntity.setType(IdRelationEnum.Store.STORE.getType());
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
            .thenReturn(Optional.of(thirdIdRelationEntity));
    }

    private RichStoreInfoDTO createTestStoreInfo() {
        RichStoreInfoDTO richStoreInfoDTO = new RichStoreInfoDTO();
        StoreInfoDTO storeInfoDTO = new StoreInfoDTO();
        storeInfoDTO.setId(STORE_ID);
        storeInfoDTO.setMerchantId(MERCHANT_ID);
        storeInfoDTO.setStoreName("测试门店");
        storeInfoDTO.setAddress("测试地址");
        
        LongLatDTO storeLongLat = new LongLatDTO();
        storeLongLat.setLongitude(116.397128);
        storeLongLat.setLatitude(39.916527);
        storeInfoDTO.setLongLat(storeLongLat);
        
        storeInfoDTO.setMinAdvanceBookingTime(new BigDecimal("30.0"));
        storeInfoDTO.setMinAdvanceBookingUnit((byte)1);
        storeInfoDTO.setOrderInterval(30);
        storeInfoDTO.setStoreStatus((byte)1);
        storeInfoDTO.setDealership(10);
        
        // 设置营业时间
        List<BusinessTimeDTO> businessTimeList = new ArrayList<>();
        BusinessTimeDTO businessTime = new BusinessTimeDTO();
        businessTime.setBusinessFrom(900);
        businessTime.setBusinessTo(2100);
        businessTimeList.add(businessTime);
        richStoreInfoDTO.setBusinessTimeList(businessTimeList);

        // 设置联系方式
        List<StoreContactDTO> storeContactList = new ArrayList<>();
        StoreContactDTO storeContact = new StoreContactDTO();
        storeContact.setLinkName("测试联系人");
        storeContact.setMobile("***********");
        storeContact.setContactType((byte)1);
        storeContactList.add(storeContact);
        richStoreInfoDTO.setStoreContactList(storeContactList);

        richStoreInfoDTO.setStoreInfoVo(storeInfoDTO);
        
        // 添加服务圈
        List<ServicePickupDTO> servicePickupList = new ArrayList<>();
        ServicePickupDTO servicePickup = new ServicePickupDTO();
        servicePickup.setId(1L);
        servicePickup.setName("测试服务圈");
        servicePickup.setPickupType((byte)1);
        servicePickup.setEnabled((byte)1);
        servicePickup.setFeeType((byte)0);
        servicePickup.setFee(0);
        servicePickup.setMinAdvanceBookingTime(new BigDecimal("30"));
        servicePickup.setBusinessFrom(900);
        servicePickup.setBusinessTo(2100);
        
        List<LongLatDTO> pickupLongLatList = new ArrayList<>();
        LongLatDTO pickupLongLat = new LongLatDTO();
        pickupLongLat.setLongitude(116.397128);
        pickupLongLat.setLatitude(39.916527);
        pickupLongLatList.add(pickupLongLat);
        servicePickup.setLongLatList(pickupLongLatList);
        
        servicePickupList.add(servicePickup);
        richStoreInfoDTO.setServicePickupList(servicePickupList);
        
        return richStoreInfoDTO;
    }

    private void setupSaasStoreClient() {
        ApiResultResp<com.ql.dto.store.StoreInfoDTO> storeInfoResp = new ApiResultResp<>();
        storeInfoResp.setCode("0");
        storeInfoResp.setMsg("success");
        
        com.ql.dto.store.StoreInfoDTO storeInfoDTO = new com.ql.dto.store.StoreInfoDTO();
        storeInfoDTO.setStoreName("测试门店1");
        storeInfoDTO.setAddress("北京市朝阳区");
        storeInfoDTO.setStoreStatus((byte) 1);
        storeInfoResp.setData(storeInfoDTO);
        
        when(saasStoreClient.getStoreInfo(eq(MERCHANT_ID), eq(STORE_ID), eq(CHANNEL_ID)))
            .thenReturn(storeInfoResp);
    }

    @Test
    void shouldHandleStoreAndRangeChangeNotify() {
        // 设置 mock 对象
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode("123456789");
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(123456L), eq(4L)))
                .thenReturn(Optional.of(apiConnEntity));

        // 设置门店映射关系
        ThirdIdRelationEntity storeRelation = new ThirdIdRelationEntity();
        storeRelation.setThirdId("7301553626531273015");
        storeRelation.setSaasId(123456L);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.STORE.getType()), eq(123456L)))
                .thenReturn(Optional.of(storeRelation));

        // 设置 storeService mock
        RichStoreInfoDTO storeInfoDTO = createTestStoreInfo();
        Map<Long, RichStoreInfoDTO> storeMap = new HashMap<>();
        storeMap.put(123456L, storeInfoDTO);
        when(storeService.getStoreMapping(eq(4L), eq(123456L), anyList())).thenReturn(storeMap);

        // 设置 HelloClientWrapper mock - 修改门店
        ResultResp<StoreModifyNotifyResp> modifyResult = new ResultResp<>();
        modifyResult.setCode("0");
        modifyResult.setMsg("success");
        StoreModifyNotifyResp modifyResp = new StoreModifyNotifyResp();
        modifyResp.setSiteId("7301553626531273015");
        modifyResult.setData(modifyResp);
        when(helloClientWrapper.storeModifyNotify(eq(123456L), any())).thenReturn(modifyResult);

        // 设置服务圈映射关系
        List<ThirdIdRelationEntity> serviceAreaRelations = new ArrayList<>();
        ThirdIdRelationEntity serviceArea = new ThirdIdRelationEntity();
        serviceArea.setId(1L);
        serviceArea.setThirdId("service_area_1");
        serviceArea.setType(IdRelationEnum.Store.CICLE.getType());
        serviceArea.setSaasId(1L);
        serviceAreaRelations.add(serviceArea);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndInSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.CICLE.getType()), anyList()))
                .thenReturn(serviceAreaRelations);

        // 设置 HelloClientWrapper mock - 服务区域
        ResultResp<ServiceCircleNotifyResp> serviceResult = new ResultResp<>();
        serviceResult.setCode("0");
        serviceResult.setMsg("success");
        ServiceCircleNotifyResp serviceResp = new ServiceCircleNotifyResp();
        serviceResp.setResult(true);
        List<ServiceCircleNotifyResp.ServiceAreaResp> serviceAreaList = new ArrayList<>();
        ServiceCircleNotifyResp.ServiceAreaResp areaResp = new ServiceCircleNotifyResp.ServiceAreaResp();
        areaResp.setId(7301553626531273016L);
        areaResp.setAreaName("test_area");
        areaResp.setServiceType(1);
        serviceAreaList.add(areaResp);
        serviceResp.setServiceAreaList(serviceAreaList);
        serviceResult.setData(serviceResp);
        when(helloClientWrapper.serviceAreaNotify(eq(123456L), any())).thenReturn(serviceResult);

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(123456L);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", 4L, 123456L);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.STORE_SAVE.getBusiType());

        // 执行被测方法
        helloStoreService.storeAndRangeChangeNotify(platformSyncParam);

        // 验证交互
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(123456L), eq(4L));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.STORE.getType()), eq(123456L));
        verify(storeService).getStoreMapping(eq(4L), eq(123456L), anyList());
        verify(helloClientWrapper).storeModifyNotify(eq(123456L), any());
        verify(helloClientWrapper).serviceAreaNotify(eq(123456L), any());
    }

    @Test
    void shouldHandleStoreOnlineOfflineChangeNotify() {
        // 设置测试所需的特定mock
        setupApiConnRepository();
        
        // 设置 ThirdIdRelationRepository mock
        ThirdIdRelationEntity relationEntity = new ThirdIdRelationEntity();
        relationEntity.setThirdId(THIRD_ID);
        relationEntity.setType((byte) 2);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
            eq(CHANNEL_ID), eq(MERCHANT_ID), eq((byte) 2), eq(STORE_ID)))
            .thenReturn(Optional.of(relationEntity));
        
        setupSaasStoreClient();
        setupHelloClientWrapperForStoreOnline();
        
        // 准备测试数据
        PlatformSyncParam param = new PlatformSyncParam();
        param.setMerchantId(MERCHANT_ID);
        param.setData("{\"storeId\":123456,\"channelId\":4,\"status\":1}");
        
        // 执行测试
        helloStoreService.storeOnlineOfflineChangeNotify(param);
        
        // 验证调用
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
            eq(CHANNEL_ID), eq(MERCHANT_ID), eq((byte) 2), eq(STORE_ID));
        verify(saasStoreClient).getStoreInfo(eq(MERCHANT_ID), eq(STORE_ID), eq(CHANNEL_ID));
        verify(helloClientWrapper).storeOnlineNotify(eq(MERCHANT_ID), any(StoreOnlineReq.class));
    }

    private void setupHelloClientWrapperForStoreOnline() {
        StoreOnlineNotifyResp onlineResp = new StoreOnlineNotifyResp();
        onlineResp.setResult(true);  // 设置结果为 true
        ResultResp<StoreOnlineNotifyResp> onlineResult = new ResultResp<>();
        onlineResult.setCode(ErrorCode.SUCCESS.getCode());
        onlineResult.setMsg("success");
        onlineResult.setData(onlineResp);
        when(helloClientWrapper.storeOnlineNotify(anyLong(), any())).thenReturn(onlineResult);
    }

    @Test
    void shouldHandleStoreOnlineOfflineChangeNotifyWithException() {
        // 设置必要的 mock
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID)))
            .thenReturn(Optional.of(apiConnEntity));

        // 准备测试数据
        PlatformSyncParam param = new PlatformSyncParam();
        param.setMerchantId(MERCHANT_ID);
        StoreInfoReq storeInfoReq = new StoreInfoReq();
        storeInfoReq.setStoreId(STORE_ID.toString());
        param.setData(serialize(storeInfoReq));

        // 设置 mock 抛出异常
        when(saasStoreClient.getStoreInfo(eq(MERCHANT_ID), eq(STORE_ID), eq(CHANNEL_ID)))
            .thenThrow(new BizException(ClientName.HELLO, "7000", "商家不存在"));

        // 执行测试
        assertThrows(BizException.class, () -> {
            helloStoreService.storeOnlineOfflineChangeNotify(param);
        });

        // 验证调用
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID));
        verify(saasStoreClient).getStoreInfo(eq(MERCHANT_ID), eq(STORE_ID), eq(CHANNEL_ID));
    }

    private String serialize(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void shouldHandleStoreRangeChangeNotify() {
        // 设置 mock 对象
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode("123456789");
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(123456L), eq(4L)))
                .thenReturn(Optional.of(apiConnEntity));

        // 设置门店映射关系
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.STORE.getType()), eq(123456L)))
                .thenReturn(Optional.of(createThirdIdRelation("7301553626531273015")));

        // 设置 storeService mock
        RichStoreInfoDTO storeInfoDTO = createTestStoreInfo();
        Map<Long, RichStoreInfoDTO> storeMap = new HashMap<>();
        storeMap.put(123456L, storeInfoDTO);
        when(storeService.getStoreMapping(eq(4L), eq(123456L), anyList())).thenReturn(storeMap);

        // 设置服务圈映射关系
        List<ThirdIdRelationEntity> serviceAreaRelations = new ArrayList<>();
        ThirdIdRelationEntity serviceArea = new ThirdIdRelationEntity();
        serviceArea.setId(1L);
        serviceArea.setSaasId(1L);
        serviceArea.setThirdId("service_area_1");
        serviceArea.setType(IdRelationEnum.Store.CICLE.getType());
        serviceAreaRelations.add(serviceArea);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndInSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.CICLE.getType()), anyList()))
                .thenReturn(serviceAreaRelations);

        // 设置 HelloClientWrapper mock
        ResultResp<ServiceCircleNotifyResp> serviceResult = new ResultResp<>();
        serviceResult.setCode("0");
        serviceResult.setMsg("success");
        ServiceCircleNotifyResp serviceResp = new ServiceCircleNotifyResp();
        serviceResp.setResult(true);
        List<ServiceCircleNotifyResp.ServiceAreaResp> serviceAreaList = new ArrayList<>();
        ServiceCircleNotifyResp.ServiceAreaResp areaResp = new ServiceCircleNotifyResp.ServiceAreaResp();
        areaResp.setId(7301553626531273016L);
        areaResp.setAreaName("test_area");
        areaResp.setServiceType(1);
        serviceAreaList.add(areaResp);
        serviceResp.setServiceAreaList(serviceAreaList);
        serviceResult.setData(serviceResp);
        when(helloClientWrapper.serviceAreaNotify(eq(123456L), any())).thenReturn(serviceResult);

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(123456L);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", 4L, 123456L);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.CIRCLE_SAVE.getBusiType());

        // 执行被测方法
        helloStoreService.storeRangeChangeNotify(platformSyncParam);

        // 验证交互
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(123456L), eq(4L));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.STORE.getType()), eq(123456L));
        verify(storeService).getStoreMapping(eq(4L), eq(123456L), anyList());
        verify(helloClientWrapper).serviceAreaNotify(eq(123456L), any());
    }

    private ThirdIdRelationEntity createThirdIdRelation(String thirdId) {
        ThirdIdRelationEntity entity = new ThirdIdRelationEntity();
        entity.setThirdId(thirdId);
        entity.setType(IdRelationEnum.Store.STORE.getType());
        return entity;
    }

    @Test
    void shouldHandleStoreRangeChangeNotifyWhenApiConnNotFound() throws Exception {
        // 模拟 ApiConnRepository 返回空
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID)))
            .thenReturn(Optional.empty());

        // 模拟 ThirdIdRelationRepository 返回门店映射关系
        ThirdIdRelationEntity storeRelation = new ThirdIdRelationEntity();
        storeRelation.setThirdId(THIRD_ID);
        storeRelation.setType(IdRelationEnum.Store.STORE.getType());
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
            .thenReturn(Optional.of(storeRelation));

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(MERCHANT_ID);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", CHANNEL_ID, STORE_ID);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.STORE_SAVE.getBusiType());

        // 执行被测方法并验证异常
        BizException exception = assertThrows(BizException.class, () -> {
            helloStoreService.storeRangeChangeNotify(platformSyncParam);
        });
        
        assertEquals(ClientName.HELLO, exception.getClientName());
        assertEquals("7000", exception.getCode());
        assertEquals("商家不存在", exception.getMessage());
        
        // 验证交互
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
    }

    @Test
    void shouldHandleStoreRangeChangeNotifyWhenStoreIdRelationNotFound() throws Exception {
        // 模拟 ThirdIdRelationRepository 返回空
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
            .thenReturn(Optional.empty());

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(MERCHANT_ID);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", CHANNEL_ID, STORE_ID);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.STORE_SAVE.getBusiType());

        // 执行被测方法
        helloStoreService.storeRangeChangeNotify(platformSyncParam);

        // 验证交互
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        
        // 验证没有调用后续方法
        verify(apiConnRepository, never()).findByMerchantIdAndChannelIdOptional(any(), any());
        verify(storeService, never()).getStoreMapping(any(), any(), any());
        verify(helloClientWrapper, never()).serviceAreaNotify(any(), any());
    }

    @Test
    void shouldHandleStoreRangeChangeNotifyWhenStoreInfoNotFound() throws Exception {
        // 模拟 ApiConnRepository
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID)))
            .thenReturn(Optional.of(apiConnEntity));

        // 模拟 ThirdIdRelationRepository 返回门店映射关系
        ThirdIdRelationEntity storeRelation = new ThirdIdRelationEntity();
        storeRelation.setThirdId(THIRD_ID);
        storeRelation.setType(IdRelationEnum.Store.STORE.getType());
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
            .thenReturn(Optional.of(storeRelation));

        // 模拟 StoreService 返回空
        when(storeService.getStoreMapping(eq(CHANNEL_ID), eq(MERCHANT_ID), anyList()))
            .thenReturn(null);

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(MERCHANT_ID);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", CHANNEL_ID, STORE_ID);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.STORE_SAVE.getBusiType());

        // 执行被测方法
        helloStoreService.storeRangeChangeNotify(platformSyncParam);

        // 验证交互
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(CHANNEL_ID), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(storeService).getStoreMapping(eq(CHANNEL_ID), eq(MERCHANT_ID), anyList());
        
        // 验证没有调用后续方法
        verify(helloClientWrapper, never()).serviceAreaNotify(any(), any());
    }

    @Test
    void shouldHandleStoreRangeChangeNotifyWhenHelloClientFails() throws Exception {
        // 设置测试环境和模拟对象
        // 1. 模拟 ApiConnRepository
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO)))
            .thenReturn(Optional.of(apiConnEntity));

        // 2. 模拟 ThirdIdRelationRepository 返回门店映射关系
        ThirdIdRelationEntity storeRelation = new ThirdIdRelationEntity();
        storeRelation.setThirdId(THIRD_ID);
        storeRelation.setType(IdRelationEnum.Store.STORE.getType());
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.of(storeRelation));

        // 3. 模拟服务圈映射关系
        List<ThirdIdRelationEntity> serviceAreaRelations = new ArrayList<>();
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndInSaasId(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Store.CICLE.getType()), anyList()))
            .thenReturn(serviceAreaRelations);

        // 4. 模拟 StoreService 返回门店信息
        RichStoreInfoDTO richStoreInfoDTO = createTestStoreInfo();
        Map<Long, RichStoreInfoDTO> storeMap = new HashMap<>();
        storeMap.put(STORE_ID, richStoreInfoDTO);
        when(storeService.getStoreMapping(eq(CHANNEL_ID), eq(MERCHANT_ID), anyList()))
            .thenReturn(storeMap);

        // 5. 模拟 HelloClientWrapper 返回失败响应
        ResultResp<ServiceCircleNotifyResp> resultResp = new ResultResp<>();
        resultResp.setCode("500");
        resultResp.setMsg("服务器内部错误");
        when(helloClientWrapper.serviceAreaNotify(eq(MERCHANT_ID), any()))
            .thenReturn(resultResp);

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(MERCHANT_ID);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", CHANNEL_ID, STORE_ID);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.STORE_SAVE.getBusiType());

        // 执行被测方法
        helloStoreService.storeRangeChangeNotify(platformSyncParam);

        // 验证结果和交互
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(CHANNEL_ID));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(storeService).getStoreMapping(eq(CHANNEL_ID), eq(MERCHANT_ID), anyList());
        verify(helloClientWrapper).serviceAreaNotify(eq(MERCHANT_ID), any());
        
        // 验证没有调用删除服务圈映射数据的方法
        verify(thirdIdRelationRepository, never()).findByChannelIdAndMerchantIdAndTypeAndNotInSaasId(
                any(), any(), any(), any());
        verify(thirdIdRelationRepository, never()).deleteInBatch(any());
    }

    @Test
    void shouldHandleStoreRangeChangeNotifyWithEmptyServicePickupList() {
        // 设置 mock 对象
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode("123456789");
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(123456L), eq(4L)))
                .thenReturn(Optional.of(apiConnEntity));

        // 设置门店映射关系
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.STORE.getType()), eq(123456L)))
                .thenReturn(Optional.of(createThirdIdRelation("7301553626531273015")));

        // 设置 storeService mock，返回空的服务区域列表
        RichStoreInfoDTO storeInfoDTO = createTestStoreInfo();
        storeInfoDTO.setServicePickupList(new ArrayList<>()); // 设置空的服务区域列表
        Map<Long, RichStoreInfoDTO> storeMap = new HashMap<>();
        storeMap.put(123456L, storeInfoDTO);
        when(storeService.getStoreMapping(eq(4L), eq(123456L), anyList())).thenReturn(storeMap);

        // 设置服务圈映射关系
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndInSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.CICLE.getType()), anyList()))
                .thenReturn(new ArrayList<>());

        // 设置 HelloClientWrapper mock
        ResultResp<ServiceCircleNotifyResp> serviceResult = new ResultResp<>();
        serviceResult.setCode("0");
        serviceResult.setMsg("success");
        ServiceCircleNotifyResp serviceResp = new ServiceCircleNotifyResp();
        serviceResp.setResult(true);
        serviceResp.setServiceAreaList(new ArrayList<>());
        serviceResult.setData(serviceResp);
        when(helloClientWrapper.serviceAreaNotify(eq(123456L), any())).thenReturn(serviceResult);

        // 准备测试数据
        PlatformSyncParam platformSyncParam = new PlatformSyncParam();
        platformSyncParam.setMerchantId(123456L);
        String data = String.format("{\"channelId\":%d,\"storeId\":%d}", 4L, 123456L);
        platformSyncParam.setData(data);
        platformSyncParam.setBusiType(ModificationBusiTypeEnum.CIRCLE_SAVE.getBusiType());

        // 执行被测方法
        helloStoreService.storeRangeChangeNotify(platformSyncParam);

        // 验证交互
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(123456L), eq(4L));
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(4L), eq(123456L), eq(IdRelationEnum.Store.STORE.getType()), eq(123456L));
        verify(storeService).getStoreMapping(eq(4L), eq(123456L), anyList());
        verify(helloClientWrapper).serviceAreaNotify(eq(123456L), any());
    }

    @Test
    void shouldHandleInitStockWhenQueryResultEmpty() {
        // 模拟商户ID
        Long merchantId = MERCHANT_ID;
        
        // 模拟ApiConnRepository返回商户信息
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO)))
            .thenReturn(Optional.of(apiConnEntity));
        
        // 模拟车辆映射关系
        List<ThirdVehicleIdRelationEntity> vehicleRelations = new ArrayList<>();
        ThirdVehicleIdRelationEntity vehicleRelation = new ThirdVehicleIdRelationEntity();
        vehicleRelation.setMerchantId(MERCHANT_ID);
        vehicleRelation.setStoreId(STORE_ID);
        vehicleRelation.setChannelId(HELLO);
        vehicleRelation.setType(IdRelationEnum.Vehicle.VEHICLE_INFO.getType());
        vehicleRelation.setThirdId("1001");
        vehicleRelation.setSaasId(1001L);
        vehicleRelation.setCreateTime(System.currentTimeMillis());
        vehicleRelation.setOpTime(System.currentTimeMillis());
        vehicleRelations.add(vehicleRelation);
        
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType())))
            .thenReturn(vehicleRelations);
        
        // 模拟HelloClientWrapper返回成功但occupyList为空
        ResultResp<QueryCarOccupyInventoryResp> resultResp = new ResultResp<>();
        resultResp.setCode("0");
        resultResp.setMsg("success");
        QueryCarOccupyInventoryResp queryCarOccupyInventoryResp = new QueryCarOccupyInventoryResp();
        queryCarOccupyInventoryResp.setOccupyList(new ArrayList<>());
        resultResp.setData(queryCarOccupyInventoryResp);
        
        when(helloClientWrapper.queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class)))
            .thenReturn(resultResp);
        
        // 执行测试
        SaasResultResp result = helloBaseService.initStock(merchantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        // 不检查具体的消息内容，只检查返回码
        
        // 验证调用
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()));
        verify(helloClientWrapper).queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class));
        verify(saasVehicleClient, never()).getVehicleDetailById(any());
        verify(thirdVehicleIdRelationRepository, never()).saveAll(anyList());
    }
    
    @Test
    void shouldInitStockSuccessfully() {
        // 模拟商户ID
        Long merchantId = MERCHANT_ID;
        
        // 模拟ApiConnRepository返回商户信息
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO)))
            .thenReturn(Optional.of(apiConnEntity));
        
        // 模拟车辆映射关系
        List<ThirdVehicleIdRelationEntity> vehicleRelations = new ArrayList<>();
        ThirdVehicleIdRelationEntity vehicleRelation = new ThirdVehicleIdRelationEntity();
        vehicleRelation.setMerchantId(MERCHANT_ID);
        vehicleRelation.setStoreId(STORE_ID);
        vehicleRelation.setChannelId(HELLO);
        vehicleRelation.setType(IdRelationEnum.Vehicle.VEHICLE_INFO.getType());
        vehicleRelation.setThirdId("1001");
        vehicleRelation.setSaasId(1001L);
        vehicleRelation.setCreateTime(System.currentTimeMillis());
        vehicleRelation.setOpTime(System.currentTimeMillis());
        vehicleRelations.add(vehicleRelation);
        
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType())))
            .thenReturn(vehicleRelations);
        
        // 模拟HelloClientWrapper返回成功
        QueryCarOccupyInventoryResp queryResp = new QueryCarOccupyInventoryResp();
        queryResp.setOccupyList(new ArrayList<>());
        
        ResultResp<QueryCarOccupyInventoryResp> resultResp = new ResultResp<>();
        resultResp.setCode("0");
        resultResp.setMsg("success");
        resultResp.setData(queryResp);
        
        when(helloClientWrapper.queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class)))
            .thenReturn(resultResp);
        
        // 执行测试
        SaasResultResp result = helloBaseService.initStock(merchantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        
        // 验证调用
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()));
        verify(helloClientWrapper).queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class));
        verify(saasVehicleClient, never()).getVehicleDetailById(any());
        verify(thirdVehicleIdRelationRepository, never()).saveAll(anyList());
    }
    
    @Test
    void shouldHandleInitStockWhenApiConnNotFound() {
        // 模拟商户ID
        Long merchantId = MERCHANT_ID;
        
        // 执行测试
        SaasResultResp result = helloBaseService.initStock(merchantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
    }
    
    @Test
    void shouldHandleInitStockWhenVehicleRelationsEmpty() {
        // 模拟商户ID
        Long merchantId = MERCHANT_ID;
        
        // 执行测试
        SaasResultResp result = helloBaseService.initStock(merchantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
    }
    
    @Test
    void shouldHandleInitStockWhenHelloClientFails() {
        // 模拟商户ID
        Long merchantId = MERCHANT_ID;
        
        // 模拟ApiConnRepository返回商户信息
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO)))
            .thenReturn(Optional.of(apiConnEntity));
        
        // 模拟车辆映射关系数据
        List<ThirdVehicleIdRelationEntity> vehicleRelations = new ArrayList<>();
        ThirdVehicleIdRelationEntity vehicleRelation = new ThirdVehicleIdRelationEntity();
        vehicleRelation.setMerchantId(MERCHANT_ID);
        vehicleRelation.setStoreId(STORE_ID);
        vehicleRelation.setChannelId(HELLO);
        vehicleRelation.setType(IdRelationEnum.Vehicle.VEHICLE_INFO.getType());
        vehicleRelation.setThirdId("1001");
        vehicleRelation.setSaasId(1001L);
        vehicleRelation.setCreateTime(System.currentTimeMillis());
        vehicleRelation.setOpTime(System.currentTimeMillis());
        vehicleRelations.add(vehicleRelation);
        
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType())))
            .thenReturn(vehicleRelations);
        
        // 模拟HelloClientWrapper返回失败
        ResultResp<QueryCarOccupyInventoryResp> resultResp = new ResultResp<>();
        resultResp.setCode("500");
        resultResp.setMsg("服务器内部错误");
        
        when(helloClientWrapper.queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class)))
            .thenReturn(resultResp);
        
        // 执行测试
        SaasResultResp result = helloBaseService.initStock(merchantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        
        // 验证调用
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()));
        verify(helloClientWrapper).queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class));
        verify(thirdVehicleIdRelationRepository, never()).saveAll(anyList());
    }
    
    @Test
    void shouldHandleInitStockWhenExceptionOccurs() {
        // 模拟商户ID
        Long merchantId = MERCHANT_ID;
        
        // 模拟ApiConnRepository返回商户信息
        ApiConnEntity apiConnEntity = new ApiConnEntity();
        apiConnEntity.setChannelVendorCode(THIRD_MERCHANT_ID);
        when(apiConnRepository.findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO)))
            .thenReturn(Optional.of(apiConnEntity));
        
        // 模拟车辆映射关系数据
        List<ThirdVehicleIdRelationEntity> vehicleRelations = new ArrayList<>();
        ThirdVehicleIdRelationEntity vehicleRelation = new ThirdVehicleIdRelationEntity();
        vehicleRelation.setMerchantId(MERCHANT_ID);
        vehicleRelation.setStoreId(STORE_ID);
        vehicleRelation.setChannelId(HELLO);
        vehicleRelation.setType(IdRelationEnum.Vehicle.VEHICLE_INFO.getType());
        vehicleRelation.setThirdId("1001");
        vehicleRelation.setSaasId(1001L);
        vehicleRelation.setCreateTime(System.currentTimeMillis());
        vehicleRelation.setOpTime(System.currentTimeMillis());
        vehicleRelations.add(vehicleRelation);
        
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType())))
            .thenReturn(vehicleRelations);
        
        // 模拟HelloClientWrapper抛出异常
        when(helloClientWrapper.queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class)))
            .thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试
        SaasResultResp result = helloBaseService.initStock(merchantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        // 不检查具体的消息内容，只检查返回码
        
        // 验证调用
        verify(apiConnRepository).findByMerchantIdAndChannelIdOptional(eq(MERCHANT_ID), eq(HELLO));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndType(
                eq(HELLO), eq(MERCHANT_ID), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()));
        verify(helloClientWrapper).queryOccupyInventory(eq(MERCHANT_ID), any(CarOccupyInventoryQueryReq.class));
        verify(thirdVehicleIdRelationRepository, never()).saveAll(anyList());
    }

    @Test
    void shouldSuccessfullySettleVehicle() {
        // 准备测试数据
        String thirdMerchantId = THIRD_MERCHANT_ID;
        Long merchantId = MERCHANT_ID;
        VehicleSettleReq req = new VehicleSettleReq();
        req.setVehicleId(1001L);
        req.setStoreId(STORE_ID);
        req.setVehicleModelId(2001L);
        req.setHelloOrderId("hello-123");
        req.setPickupDate(System.currentTimeMillis());
        req.setReturnDate(System.currentTimeMillis());
        req.setEndIntervalTime(System.currentTimeMillis());
        req.setSourceOrderId("source-123");
        
        // 模拟门店关系查询
        ThirdIdRelationEntity storeRelationEntity = new ThirdIdRelationEntity();
        storeRelationEntity.setThirdId("789");
        storeRelationEntity.setSaasId(STORE_ID);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.of(storeRelationEntity));
        
        // 模拟车型关系查询
        ThirdVehicleIdRelationEntity vehicleModelRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleModelRelationEntity.setThirdId("model-789");
        vehicleModelRelationEntity.setSaasId(2001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L)))
                .thenReturn(Optional.of(vehicleModelRelationEntity));
        
        // 模拟车辆关系查询
        ThirdVehicleIdRelationEntity vehicleRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleRelationEntity.setThirdId("vehicle-789");
        vehicleRelationEntity.setSaasId(1001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L)))
                .thenReturn(Optional.of(vehicleRelationEntity));
        
        // 模拟排车成功
        HelloVehicleSettleReq settleReq = new HelloVehicleSettleReq();
        ResultResp<VehicleSettleResp> resultResp = new ResultResp<>();
        resultResp.setCode("0");
        resultResp.setMsg("success");
        VehicleSettleResp settleResp = new VehicleSettleResp();
        settleResp.setResult(true);
        resultResp.setData(settleResp);
        when(helloClientWrapper.vehicleSettleNotify(eq(thirdMerchantId), any(HelloVehicleSettleReq.class)))
                .thenReturn(resultResp);
        
        // 执行测试
        SaasResultResp result = helloBaseService.vehicleSettle(thirdMerchantId, merchantId, req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("0", result.getCode());
        assertEquals("success", result.getMsg());
        
        // 验证调用
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L));
        verify(helloClientWrapper).vehicleSettleNotify(eq(thirdMerchantId), any(HelloVehicleSettleReq.class));
    }

    @Test
    void shouldFailSettleVehicleWhenStoreRelationNotFound() {
        // 准备测试数据
        String thirdMerchantId = THIRD_MERCHANT_ID;
        Long merchantId = MERCHANT_ID;
        VehicleSettleReq req = new VehicleSettleReq();
        req.setVehicleId(1001L);
        req.setStoreId(STORE_ID);
        req.setVehicleModelId(2001L);
        
        // 模拟门店关系查询返回空
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.empty());
        
        // 执行测试
        SaasResultResp result = helloBaseService.vehicleSettle(thirdMerchantId, merchantId, req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        assertEquals("未找到门店映射关系", result.getMsg());
        
        // 验证调用
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(thirdVehicleIdRelationRepository, never()).findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                any(), any(), any(), any(), any());
        verify(thirdVehicleIdRelationRepository, never()).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                any(), any(), any(), any());
        verify(helloClientWrapper, never()).vehicleSettleNotify(any(), any());
    }

    @Test
    void shouldFailSettleVehicleWhenVehicleModelRelationNotFound() {
        // 准备测试数据
        String thirdMerchantId = THIRD_MERCHANT_ID;
        Long merchantId = MERCHANT_ID;
        VehicleSettleReq req = new VehicleSettleReq();
        req.setVehicleId(1001L);
        req.setStoreId(STORE_ID);
        req.setVehicleModelId(2001L);
        
        // 模拟门店关系查询成功
        ThirdIdRelationEntity storeRelationEntity = new ThirdIdRelationEntity();
        storeRelationEntity.setThirdId("789");
        storeRelationEntity.setSaasId(STORE_ID);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.of(storeRelationEntity));
        
        // 模拟车型关系查询返回空
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L)))
                .thenReturn(Optional.empty());
        
        // 执行测试
        SaasResultResp result = helloBaseService.vehicleSettle(thirdMerchantId, merchantId, req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        assertEquals("未找到车型(商品)映射关系", result.getMsg());
        
        // 验证调用
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L));
        verify(thirdVehicleIdRelationRepository, never()).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                any(), any(), any(), any());
        verify(helloClientWrapper, never()).vehicleSettleNotify(any(), any());
    }

    @Test
    void shouldFailSettleVehicleWhenVehicleRelationNotFound() {
        // 准备测试数据
        String thirdMerchantId = THIRD_MERCHANT_ID;
        Long merchantId = MERCHANT_ID;
        VehicleSettleReq req = new VehicleSettleReq();
        req.setVehicleId(1001L);
        req.setStoreId(STORE_ID);
        req.setVehicleModelId(2001L);
        
        // 模拟门店关系查询成功
        ThirdIdRelationEntity storeRelationEntity = new ThirdIdRelationEntity();
        storeRelationEntity.setThirdId("789");
        storeRelationEntity.setSaasId(STORE_ID);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.of(storeRelationEntity));
        
        // 模拟车型关系查询成功
        ThirdVehicleIdRelationEntity vehicleModelRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleModelRelationEntity.setThirdId("model-789");
        vehicleModelRelationEntity.setSaasId(2001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L)))
                .thenReturn(Optional.of(vehicleModelRelationEntity));
        
        // 模拟车辆关系查询返回空
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L)))
                .thenReturn(Optional.empty());
        
        // 执行测试
        SaasResultResp result = helloBaseService.vehicleSettle(thirdMerchantId, merchantId, req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("-1", result.getCode());
        assertEquals("未找到车辆映射关系", result.getMsg());
        
        // 验证调用
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L));
        verify(helloClientWrapper, never()).vehicleSettleNotify(any(), any());
    }

    @Test
    void shouldFailSettleVehicleWhenHelloApiCallFails() {
        // 准备测试数据
        String thirdMerchantId = THIRD_MERCHANT_ID;
        Long merchantId = MERCHANT_ID;
        VehicleSettleReq req = new VehicleSettleReq();
        req.setVehicleId(1001L);
        req.setStoreId(STORE_ID);
        req.setVehicleModelId(2001L);
        req.setPickupDate(System.currentTimeMillis());
        req.setReturnDate(System.currentTimeMillis());
        req.setEndIntervalTime(System.currentTimeMillis());
        
        // 模拟门店关系查询成功
        ThirdIdRelationEntity storeRelationEntity = new ThirdIdRelationEntity();
        storeRelationEntity.setThirdId("789");
        storeRelationEntity.setSaasId(STORE_ID);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.of(storeRelationEntity));
        
        // 模拟车型关系查询成功
        ThirdVehicleIdRelationEntity vehicleModelRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleModelRelationEntity.setThirdId("model-789");
        vehicleModelRelationEntity.setSaasId(2001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L)))
                .thenReturn(Optional.of(vehicleModelRelationEntity));
        
        // 模拟车辆关系查询成功
        ThirdVehicleIdRelationEntity vehicleRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleRelationEntity.setThirdId("vehicle-789");
        vehicleRelationEntity.setSaasId(1001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L)))
                .thenReturn(Optional.of(vehicleRelationEntity));
        
        // 模拟排车API调用失败
        ResultResp<VehicleSettleResp> resultResp = new ResultResp<>();
        resultResp.setCode("500");
        resultResp.setMsg("Internal Server Error");
        VehicleSettleResp settleResp = new VehicleSettleResp();
        settleResp.setResult(false);
        resultResp.setData(settleResp);
        when(helloClientWrapper.vehicleSettleNotify(eq(thirdMerchantId), any(HelloVehicleSettleReq.class)))
                .thenReturn(resultResp);
        
        // 执行测试
        SaasResultResp result = helloBaseService.vehicleSettle(thirdMerchantId, merchantId, req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("Internal Server Error", result.getMsg());
        
        // 验证调用
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L));
        verify(helloClientWrapper).vehicleSettleNotify(eq(thirdMerchantId), any(HelloVehicleSettleReq.class));
    }

    @Test
    void shouldHandleNullResultFromHelloApiCall() {
        // 准备测试数据
        String thirdMerchantId = THIRD_MERCHANT_ID;
        Long merchantId = MERCHANT_ID;
        VehicleSettleReq req = new VehicleSettleReq();
        req.setVehicleId(1001L);
        req.setStoreId(STORE_ID);
        req.setVehicleModelId(2001L);
        req.setPickupDate(System.currentTimeMillis());
        req.setReturnDate(System.currentTimeMillis());
        req.setEndIntervalTime(System.currentTimeMillis());
        
        // 模拟门店关系查询成功
        ThirdIdRelationEntity storeRelationEntity = new ThirdIdRelationEntity();
        storeRelationEntity.setThirdId("789");
        storeRelationEntity.setSaasId(STORE_ID);
        when(thirdIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID)))
                .thenReturn(Optional.of(storeRelationEntity));
        
        // 模拟车型关系查询成功
        ThirdVehicleIdRelationEntity vehicleModelRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleModelRelationEntity.setThirdId("model-789");
        vehicleModelRelationEntity.setSaasId(2001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L)))
                .thenReturn(Optional.of(vehicleModelRelationEntity));
        
        // 模拟车辆关系查询成功
        ThirdVehicleIdRelationEntity vehicleRelationEntity = new ThirdVehicleIdRelationEntity();
        vehicleRelationEntity.setThirdId("vehicle-789");
        vehicleRelationEntity.setSaasId(1001L);
        when(thirdVehicleIdRelationRepository.findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L)))
                .thenReturn(Optional.of(vehicleRelationEntity));
        
        // 模拟响应对象为null
        ResultResp<VehicleSettleResp> resultResp = new ResultResp<>();
        resultResp.setCode("0");
        resultResp.setMsg("success");
        resultResp.setData(null);
        when(helloClientWrapper.vehicleSettleNotify(eq(thirdMerchantId), any(HelloVehicleSettleReq.class)))
                .thenReturn(resultResp);
        
        // 执行测试
        SaasResultResp result = helloBaseService.vehicleSettle(thirdMerchantId, merchantId, req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("0", result.getCode());
        assertEquals("success", result.getMsg());
        
        // 验证调用
        verify(thirdIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Store.STORE.getType()), eq(STORE_ID));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(STORE_ID), eq(IdRelationEnum.Vehicle.VEHICLE_MODEL.getType()), eq(2001L));
        verify(thirdVehicleIdRelationRepository).findByChannelIdAndMerchantIdAndTypeAndSaasId(
                eq(HELLO), eq(merchantId), eq(IdRelationEnum.Vehicle.VEHICLE_INFO.getType()), eq(1001L));
        verify(helloClientWrapper).vehicleSettleNotify(eq(thirdMerchantId), any(HelloVehicleSettleReq.class));
    }
}