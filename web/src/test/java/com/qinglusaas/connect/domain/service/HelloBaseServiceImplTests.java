package com.qinglusaas.connect.domain.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.qinglusaas.connect.client.saas.vo.request.VehicleSettleReq;
import com.qinglusaas.connect.client.saas.vo.response.SaasResultResp;
import com.qinglusaas.connect.infra.remote.hello.client.HelloClientWrapper;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.*;
import com.qinglusaas.connect.infra.remote.hello.vo.request.*;
import com.qinglusaas.connect.infra.util.SpanUtil;
import com.ql.dto.msg.PlatformSyncParam;
import com.ql.enums.open.ModificationBusiTypeEnum;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.Date;
import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @Date 2023/5/11 13:28
 */
@QuarkusTest
public class HelloBaseServiceImplTests {

    @Inject
    IHelloBaseService helloBaseService;

    @Inject
    IHelloVehicleService helloVehicleService;


    @Inject
    IHelloStoreService helloStoreService;

    @Inject
    HelloClientWrapper helloClientWrapper;

    @Test
    public void initHelloPricePull() throws JsonProcessingException {
        VehicleInitReq param = new VehicleInitReq();
        param.setMerchantId(6L);
        helloBaseService.initHelloPricePull(param);
    }

    @Test
    public void goodsPriceNotify() throws JsonProcessingException {
        PlatformSyncParam param = new PlatformSyncParam();
        param.setBusiType(204);
        param.setMerchantId(16L);
        param.setTraceId("237d57a65cd2b07c276554558fee43f3");
        param.setSpanId("6b7262b61a2103a3");
        param.setData("{\"channel\":4,\"merchantId\":16,\"storeId\":7309,\"vehicleModelId\":13111}");
        helloBaseService.goodsPriceNotify(param);
    }

    @Test
    public void insuranceAddedServiceUpdateNotify() throws JsonProcessingException {
        PlatformSyncParam param = new PlatformSyncParam();
        param.setBusiType(null);
        param.setMerchantId(16L);
        helloBaseService.insuranceAddedServiceUpdateNotify(param);
    }

    @Test
    public void storeAndRangeChangeNotify() throws JsonProcessingException {
        PlatformSyncParam param = new PlatformSyncParam();

        param.setBusiType(ModificationBusiTypeEnum.STORE_SAVE.getBusiType());
        param.setData("{\"channelId\":1,\"isPushCity\":false,\"storeId\":7309}");
        param.setMerchantId(16L);
        helloStoreService.storeAndRangeChangeNotify(param);
    }

    @Test
    public void orderList() {
        SaasOrderListReq saasOrderListReq = new SaasOrderListReq();
        SaasResultResp<OrderListResp> orderListRespSaasResultResp =
                helloBaseService.orderList(saasOrderListReq);
        System.out.println(SpanUtil.toJson(orderListRespSaasResultResp));
    }

    @Test
    public void orderDetail() {
        SaasOrderDetailReq saasOrderDetailReq = new SaasOrderDetailReq();
        saasOrderDetailReq.setMerchantId(16L);
        saasOrderDetailReq.setThirdOrderId("RC202503257306162497956610052");
        SaasResultResp<OrderDetailResp> resp =
                helloBaseService.orderDetail(saasOrderDetailReq);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void initHelloStore() {
        HelloStoreInitReq storeInitReq = new HelloStoreInitReq();
        storeInitReq.setMerchantId(16L);
        storeInitReq.setPullStoreIds(java.util.Arrays.asList("7298370349238591116"));
        SaasResultResp resp = helloStoreService.initHelloStore(storeInitReq);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void initServiceCircle() {
        ServiceCircleInitReq serviceCircleInitReq = new ServiceCircleInitReq();
        serviceCircleInitReq.setStoreIds(List.of("7306926289173414814"));
        SaasResultResp resp = helloStoreService.initServiceCircle(serviceCircleInitReq, 6L);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void initStock() {
        SaasResultResp resp = helloBaseService.initStock(16L);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void pushStockByVehicle() {
        PushStockByVehicleReq req = new PushStockByVehicleReq();
        req.setMerchantId(16L);
        req.setVehicleId(75483L);
        helloBaseService.pushStockByVehicle(req);
    }

    @Test
    public void vehicleSettle() {
        VehicleSettleReq req = new VehicleSettleReq();
        req.setHelloOrderId("7306162500754800827");
        req.setOrderId(1704097L);
        req.setSourceOrderId("RC202503257306162497956610052");
//        req.setVehicleModelId(13136L);
        req.setVehicleId(75482L);
        req.setStoreId(7309L);
        // 2025-03-28 18:45 取车时间
        req.setPickupDate(Date.from(java.time.LocalDateTime.of(2025, 3, 28, 18, 45).atZone(java.time.ZoneId.systemDefault()).toInstant()).getTime());
        // 2025-03-29 18:45 还车时间
        req.setReturnDate(Date.from(java.time.LocalDateTime.of(2025, 3, 29, 18, 45).atZone(java.time.ZoneId.systemDefault()).toInstant()).getTime());

        req.setEndIntervalTime(1743248699999L);
        helloBaseService.vehicleSettle("20000038603", 16L, req);
    }

    @Test
    public void orderInventoryOccupyQuery() {
        SaasResultResp<OrderInventoryOccupyQueryResp> resp = helloVehicleService.orderInventoryOccupyQuery(List.of(7306554629122112688L), 16L);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void pushAllStoreToHello() {
        SaasResultResp<OrderInventoryOccupyQueryResp> resp = helloStoreService.pushAllStoreToHello(6L, "20000036171");
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void pushAllCircleToHello() {
        SaasResultResp<OrderInventoryOccupyQueryResp> resp = helloStoreService.pushAllCircleToHello(6L, "20000036171");
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void queryOccupyInventory() {
        CarOccupyInventoryQueryReq queryReq = new CarOccupyInventoryQueryReq();
        queryReq.setCarIdList(List.of("7329929270982934879"));
//        queryReq.setCarIdList(List.of("7305636495560736887"));
        queryReq.setMerchantId("20000038603");
        queryReq.setStartTime("2025-09-01");
        queryReq.setEndTime("2025-09-29");
        ResultResp<QueryCarOccupyInventoryResp> resp = helloClientWrapper.queryOccupyInventory(16L, queryReq);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void queryOccupyInventory11() {
        CarOccupyInventoryQueryReq queryReq = new CarOccupyInventoryQueryReq();
        queryReq.setCarIdList(List.of("7305636495560736887"));
//        queryReq.setCarIdList(List.of("7305636495560736887"));
        queryReq.setMerchantId("20000038603");
        queryReq.setStartTime("2025-09-01");
        queryReq.setEndTime("2025-12-29");
        ResultResp<QueryCarOccupyInventoryResp> resp = helloClientWrapper.queryOccupyInventory(16L, queryReq);
        System.out.println(SpanUtil.toJson(resp));
    }

    @Test
    public void releaseInventoryNotify() {
        CarReleaseInventoryReq queryReq = new CarReleaseInventoryReq();
        queryReq.setMerchantId("20000038603");
        queryReq.setOccupyIdList(List.of(7338817259165648807L));
        queryReq.setTemp(true);
        queryReq.setCarId("7305636495560736887");
        ResultResp<CarReleaseInventoryResp> resp = helloClientWrapper.releaseInventoryNotify(16L, queryReq);
        System.out.println(SpanUtil.toJson(resp));
    }

}
