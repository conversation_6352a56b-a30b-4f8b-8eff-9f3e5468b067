# SaaS端携程履约变更接口文档

## 概述

本文档描述了为SaaS系统提供的携程履约变更相关接口。这些接口封装了携程的通用履约变更接口，为SaaS系统提供分离的业务功能。

**基础信息：**
- 接口基础路径：`/saas/ctrip-api/v1`
- 请求方式：`POST`
- 请求格式：`application/json`
- 响应格式：`application/json`
- 认证方式：SaaS签名验证

## 接口列表

### 1. 查询虚拟号接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/query-virtual-phone`
- **功能：** 查询携程订单的虚拟联系号码

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 携程订单ID |

#### 响应示例

```json
{
  "code": "200",
  "message": "success",
  "data": {
    "virtualPhone": "400-xxx-xxxx",
    "expireTime": "2024-01-01T12:00:00"
  },
  "success": true
}
```

### 2. 送车上门-点位上传接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/on-door-location-upload`
- **功能：** 送车上门时上传车辆点位信息 (messageType: 3)

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890,
    "messageType": 3,
    "content": "送车上门点位上传",
    "onDoorCreateCarTrackDTO": {
      "orderId": "67890",
      "clientId": "client123",
      "pathType": 1,
      "processType": 1,
      "currentTrackData": {
        "accuracy": 10.0,
        "coordSys": "GCJ-02",
        "latitude": 39.915,
        "longitude": 116.404,
        "locateTime": 1635492869606,
        "speed": 60.0
      }
    }
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 订单ID |
| data.messageType | Integer | 是 | 消息类型（固定为3） |
| data.content | String | 否 | 内容描述 |
| data.onDoorCreateCarTrackDTO | Object | 是 | 上门创建车辆轨迹信息 |

### 3. 送车上门-轨迹生成接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/on-door-track-generate`
- **功能：** 送车上门时生成车辆轨迹 (messageType: 4)

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890,
    "messageType": 4,
    "content": "送车上门轨迹生成",
    "onDoorUploadCarTrackDTO": {
      "orderId": "67890",
      "clientId": "client123",
      "trackData": [
        {
          "accuracy": 10.0,
          "coordSys": "GCJ-02",
          "latitude": 39.915,
          "longitude": 116.404,
          "locateTime": 1635492869606,
          "speed": 60.0
        }
      ]
    }
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 订单ID |
| data.messageType | Integer | 是 | 消息类型（固定为4） |
| data.content | String | 否 | 内容描述 |
| data.onDoorUploadCarTrackDTO | Object | 是 | 上门上传车辆轨迹信息 |

### 4. OCR履约实际车牌上传接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/ocr-license-plate-upload`
- **功能：** 上传OCR识别的实际车牌信息 (messageType: 5)

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890,
    "messageType": 5,
    "content": "OCR履约实际车牌上传",
    "ocrCarInfoDTO": {
      "orderId": "67890",
      "carPhoto": "http://example.com/photo.jpg"
    }
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 订单ID |
| data.messageType | Integer | 是 | 消息类型（固定为5） |
| data.content | String | 否 | 内容描述 |
| data.ocrCarInfoDTO | Object | 是 | OCR车辆信息 |

### 5. 供应商修改订单接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/vendor-modify-order`
- **功能：** 供应商发起的订单修改请求

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890,
    "modifyReason": "客户要求修改取车时间",
    "operator": "张三",
    "pickUpAddress": "北京市朝阳区xxx",
    "pickUpDate": "2024-01-01 10:00:00",
    "systemConfirm": true
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 订单ID |
| data.modifyReason | String | 是 | 修改原因 |
| data.operator | String | 是 | 操作员 |
| data.pickUpAddress | String | 否 | 取车地址 |
| data.pickUpDate | String | 否 | 取车日期 |
| data.systemConfirm | Boolean | 否 | 系统确认 |

### 6. 供应商修改订单回调接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/vendor-modify-order-callback`
- **功能：** 供应商修改订单的结果回调

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890,
    "modifyId": 123,
    "modifyStatus": 1,
    "cancelReason": 0,
    "modifyItems": ["pickUpTime", "pickUpAddress"],
    "notifyStatus": true
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 订单ID |
| data.modifyId | Long | 是 | 修改ID |
| data.modifyStatus | Integer | 是 | 修改状态 |
| data.cancelReason | Integer | 否 | 取消原因 |
| data.modifyItems | List\<String\> | 否 | 修改项目列表 |
| data.notifyStatus | Boolean | 否 | 通知状态 |

### 7. 供应商修改订单催促确认接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/vendor-modify-order-urge-confirm`
- **功能：** 催促供应商确认订单修改

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "orderId": 67890
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.orderId | Long | 是 | 订单ID |

### 8. 已排司机状态回调接口

#### 接口信息
- **路径：** `/saas/ctrip-api/v1/orderInfoCallbackV2/driverAssigned`
- **功能：** 通知携程订单已安排司机

#### 请求参数

```json
{
  "merchantId": 12345,
  "channelId": 1,
  "data": {
    "vendorCode": "VENDOR001",
    "operateSerialNumber": "OP20240101001",
    "ctripOrderId": 67890,
    "vendorOrderStatus": 8,
    "driverName": "李师傅",
    "contactType": 1,
    "contactNum": "13800138000"
  }
}
```

**参数说明：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| merchantId | Long | 是 | 商户ID |
| channelId | Long | 是 | 渠道ID |
| data.vendorCode | String | 是 | 供应商编码 |
| data.operateSerialNumber | String | 是 | 操作序列号（幂等性保证） |
| data.ctripOrderId | Long | 是 | 携程订单号 |
| data.vendorOrderStatus | Integer | 是 | 订单状态（8-已排司机） |
| data.driverName | String | 是 | 司机姓名 |
| data.contactType | Integer | 是 | 联系方式类型（1-手机，2-邮箱） |
| data.contactNum | String | 是 | 联系方式详情 |

## 通用响应格式

所有接口的响应格式均为：

```json
{
  "code": "200",
  "message": "success",
  "data": {},
  "success": true
}
```

**响应字段说明：**

| 字段 | 类型 | 说明 |
|------|------|------|
| code | String | 响应码（"200"表示成功，其他表示失败） |
| message | String | 响应消息 |
| data | Object | 响应数据（成功时包含具体数据，失败时为空） |
| success | Boolean | 是否成功 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| -1 | 系统异常 |
| 400 | 参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 订单状态枚举

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | FINISH | 订单完成 |
| 2 | NOT_REACHED | 未到店 |
| 4 | DELAYED_RETURN | 延迟还车 |
| 5 | DEPOSIT_REFUND | 违章退还 |
| 6 | PICKED_UP | 已收车 |
| 8 | DRIVER_ASSIGNED | 已排司机 |
| 13 | SCHEDULED | 已排车 |
| 14 | CHANGING_CARS | 行中换车 |

## 联系方式类型

| 类型码 | 类型名称 |
|--------|----------|
| 1 | 手机号码 |
| 2 | 邮箱地址 |

## 消息类型（MessageType）说明

| 类型码 | 功能说明 | 对应接口 |
|--------|----------|----------|
| 3 | 送车上门-点位上传 | `/on-door-location-upload` |
| 4 | 送车上门-轨迹生成 | `/on-door-track-generate` |
| 5 | OCR履约实际车牌上传 | `/ocr-license-plate-upload` |

## 请求签名

所有接口都需要进行SaaS签名验证，请确保在请求头中包含正确的签名信息。签名验证失败将返回401错误。

## 注意事项

1. **幂等性**：所有状态回调接口都支持幂等性，可以安全地重复调用
2. **重试机制**：建议在网络异常或5xx错误时进行重试，重试间隔建议1分钟以上
3. **超时时间**：建议设置30秒的请求超时时间
4. **错误处理**：请根据响应码和错误信息进行相应的错误处理
5. **日志记录**：建议记录所有接口调用的请求和响应日志，便于问题排查

## 示例代码

### Java示例

```java
// 使用Spring RestTemplate调用接口
@Service
public class CtripOrderService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 已排司机状态回调
     */
    public void driverAssigned(Long merchantId, Long ctripOrderId, String driverName, String contactNum) {
        String url = "http://raptor-service/saas/ctrip-api/v1/orderInfoCallbackV2/driverAssigned";
        
        PlatformBaseRequest<OrderInfoCallbackDriverAssignedReq> request = new PlatformBaseRequest<>();
        request.setMerchantId(merchantId);
        request.setChannelId(1L);
        
        OrderInfoCallbackDriverAssignedReq data = new OrderInfoCallbackDriverAssignedReq();
        data.setCtripOrderId(ctripOrderId);
        data.setOperateSerialNumber("OP" + System.currentTimeMillis());
        data.setDriverName(driverName);
        data.setContactType(1);
        data.setContactNum(contactNum);
        request.setData(data);
        
        SaasResultResp response = restTemplate.postForObject(url, request, SaasResultResp.class);
        if (!response.isSuccess()) {
            throw new RuntimeException("调用失败: " + response.getMessage());
        }
    }
    
    /**
     * 送车上门-点位上传
     */
    public void uploadOnDoorLocation(Long merchantId, Long orderId, TrackDataDTO trackData) {
        String url = "http://raptor-service/saas/ctrip-api/v1/on-door-location-upload";
        
        PlatformBaseRequest<OnDoorLocationUploadRequest> request = new PlatformBaseRequest<>();
        request.setMerchantId(merchantId);
        request.setChannelId(1L);
        
        OnDoorLocationUploadRequest data = new OnDoorLocationUploadRequest();
        data.setOrderId(orderId);
        data.setContent("送车上门点位上传");
        
        OnDoorCreateCarTrackDTO trackDTO = new OnDoorCreateCarTrackDTO();
        trackDTO.setOrderId(orderId.toString());
        trackDTO.setClientId("client123");
        trackDTO.setPathType(1);
        trackDTO.setProcessType(1);
        trackDTO.setCurrentTrackData(trackData);
        data.setOnDoorCreateCarTrackDTO(trackDTO);
        
        request.setData(data);
        
        SaasResultResp response = restTemplate.postForObject(url, request, SaasResultResp.class);
        if (!response.isSuccess()) {
            throw new RuntimeException("调用失败: " + response.getMessage());
        }
    }
    
    /**
     * OCR履约实际车牌上传
     */
    public void uploadOcrLicensePlate(Long merchantId, Long orderId, String carPhoto) {
        String url = "http://raptor-service/saas/ctrip-api/v1/ocr-license-plate-upload";
        
        PlatformBaseRequest<OcrLicensePlateUploadRequest> request = new PlatformBaseRequest<>();
        request.setMerchantId(merchantId);
        request.setChannelId(1L);
        
        OcrLicensePlateUploadRequest data = new OcrLicensePlateUploadRequest();
        data.setOrderId(orderId);
        data.setContent("OCR履约实际车牌上传");
        
        OcrCarInfoDTO ocrInfo = new OcrCarInfoDTO();
        ocrInfo.setOrderId(orderId.toString());
        ocrInfo.setCarPhoto(carPhoto);
        data.setOcrCarInfoDTO(ocrInfo);
        
        request.setData(data);
        
        SaasResultResp response = restTemplate.postForObject(url, request, SaasResultResp.class);
        if (!response.isSuccess()) {
            throw new RuntimeException("调用失败: " + response.getMessage());
        }
    }
}
```

### curl示例

```bash
# 送车上门-点位上传
curl -X POST \
  http://raptor-service/saas/ctrip-api/v1/on-door-location-upload \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -d '{
    "merchantId": 12345,
    "channelId": 1,
    "data": {
      "orderId": 67890,
      "messageType": 3,
      "content": "送车上门点位上传",
      "onDoorCreateCarTrackDTO": {
        "orderId": "67890",
        "clientId": "client123",
        "pathType": 1,
        "processType": 1,
        "currentTrackData": {
          "accuracy": 10.0,
          "coordSys": "GCJ-02",
          "latitude": 39.915,
          "longitude": 116.404,
          "locateTime": 1635492869606,
          "speed": 60.0
        }
      }
    }
  }'

# OCR履约实际车牌上传
curl -X POST \
  http://raptor-service/saas/ctrip-api/v1/ocr-license-plate-upload \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -d '{
    "merchantId": 12345,
    "channelId": 1,
    "data": {
      "orderId": 67890,
      "messageType": 5,
      "content": "OCR履约实际车牌上传",
      "ocrCarInfoDTO": {
        "orderId": "67890",
        "carPhoto": "http://example.com/photo.jpg"
      }
    }
  }'

# 已排司机状态回调
curl -X POST \
  http://raptor-service/saas/ctrip-api/v1/orderInfoCallbackV2/driverAssigned \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -d '{
    "merchantId": 12345,
    "channelId": 1,
    "data": {
      "operateSerialNumber": "OP20240101001",
      "ctripOrderId": 67890,
      "driverName": "李师傅",
      "contactType": 1,
      "contactNum": "13800138000"
    }
  }'
```

---

**文档版本：** v1.0  
**更新时间：** 2024-01-01  
**维护团队：** Raptor开发团队 