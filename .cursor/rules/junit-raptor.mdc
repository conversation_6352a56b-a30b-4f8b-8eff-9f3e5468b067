---
description: 执行单元测试
globs: 
alwaysApply: false
---


1. maven的setting文件使用D:\java\setting_ql.xml
2. 配置文件使用-Dquarkus.test.profile=local
3. <systemPath>${project.basedir}/lib/taobao-sdk-java-auto_1594341471306-20240822.jar</systemPath> 改为 <systemPath>D:/IdeaProjects/raptor/infra/lib/taobao-sdk-java-auto_1594341471306-20240822.jar</systemPath>
4. 可以参考mvn test "-Dquarkus.test.profile=local" -s "D:\java\settings_ql.xml"，但dtest的方法不确定的
5. 持续执行单测，直到执行成功
6. 先改测试代码的问题，如果测试代码确实没问题，是业务代码的问题，再修复业务代码，但也仅限于修复bug
7. 要求覆盖率100%
8. 改完测试代码bug再执行单测