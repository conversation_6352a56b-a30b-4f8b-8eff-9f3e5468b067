package com.qinglusaas.connect.domain.service.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.qinglusaas.connect.client.common.constants.ChannelEnum;
import com.qinglusaas.connect.client.common.error.ClientName;
import com.qinglusaas.connect.client.hello.vo.request.*;
import com.qinglusaas.connect.client.saas.vo.request.VehicleSettleReq;
import com.qinglusaas.connect.domain.converter.hello.HelloOccupyToVehicleBusyConverter;
import com.qinglusaas.connect.domain.converter.hello.HelloVehicleBusyConverter;
import com.qinglusaas.connect.domain.service.IHelloVehicleService;
import com.qinglusaas.connect.domain.util.DateTimeUtil;
import com.qinglusaas.connect.infra.persistence.dao.ApiConnRepository;
import com.qinglusaas.connect.infra.persistence.dao.ThirdPlatformSourceInfoRepository;
import com.qinglusaas.connect.infra.persistence.dao.store.ThirdIdRelationRepository;
import com.qinglusaas.connect.infra.persistence.entity.common.ApiConnEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.ThirdIdRelationEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.ThirdVehicleIdRelationEntity;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.*;
import com.qinglusaas.connect.infra.remote.hello.vo.request.*;
import com.qinglusaas.connect.client.hello.dto.RentalDTO;
import com.qinglusaas.connect.client.hello.dto.StoreRentalDTO;
import com.qinglusaas.connect.client.hello.dto.VehicleModelPriceDTO;
import com.qinglusaas.connect.client.hello.vo.request.dto.DriverInfoDTO;
import com.qinglusaas.connect.client.hello.vo.response.GetOrderInfoResp;
import com.qinglusaas.connect.client.hello.vo.response.GetRenewOrderInfoResp;
import com.qinglusaas.connect.client.hello.vo.response.GetVehiclePriceDetailResp;
import com.qinglusaas.connect.client.saas.constants.FreeDepositDegree;
import com.qinglusaas.connect.client.saas.constants.FreeDepositWay;
import com.qinglusaas.connect.client.saas.constants.PickUpDropOffType;
import com.qinglusaas.connect.client.saas.error.ErrorCode;
import com.qinglusaas.connect.client.saas.vo.response.SaasResultResp;
import com.qinglusaas.connect.domain.converter.hello.*;
import com.qinglusaas.connect.domain.mapper.OrderMapper;
import com.qinglusaas.connect.domain.model.city.City;
import com.qinglusaas.connect.domain.model.order.CancelOrder;
import com.qinglusaas.connect.domain.model.order.CancelRenewOrder;
import com.qinglusaas.connect.domain.model.order.CreateRenewOrder;
import com.qinglusaas.connect.domain.model.order.GetOrderAggregate;
import com.qinglusaas.connect.domain.model.order.GetRenewalOrderAggregate;
import com.qinglusaas.connect.domain.model.order.ValidateCancelOrder;
import com.qinglusaas.connect.domain.model.order.ValidateCancelRenewOrder;
import com.qinglusaas.connect.domain.model.order.ValidateCreateRenewOrder;
import com.qinglusaas.connect.domain.model.store.StoreVehicleModelPriceAggregate;
import com.qinglusaas.connect.domain.model.vehicle.VehicleModelPriceDetailAggregate;
import com.qinglusaas.connect.domain.service.IHelloBaseService;
import com.qinglusaas.connect.domain.service.IVehicleCommonService;
import com.qinglusaas.connect.domain.util.ModelUtil;
import com.qinglusaas.connect.infra.converter.saas.SaasFieldConverter;
import com.qinglusaas.connect.infra.exception.BizException;
import com.qinglusaas.connect.infra.logger.LogName;
import com.qinglusaas.connect.infra.persistence.dao.AreaRepository;
import com.qinglusaas.connect.infra.persistence.dao.StoreInfoRepository;
import com.qinglusaas.connect.infra.persistence.dao.vehicle.ThirdVehicleIdRelationRepository;
import com.qinglusaas.connect.infra.persistence.dao.vehicle.VehicleBindRepository;
import com.qinglusaas.connect.infra.persistence.entity.common.AreaEntity;
import com.qinglusaas.connect.infra.persistence.entity.store.StoreInfoEntity;
import com.qinglusaas.connect.infra.persistence.entity.vehicle.VehicleBindEntity;
import com.qinglusaas.connect.infra.remote.hello.client.HelloClientWrapper;
import com.qinglusaas.connect.infra.remote.hello.constants.APIMethod;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.CarOccupyInventoryResp;
import com.qinglusaas.connect.infra.remote.hello.vo.reponse.CarReleaseInventoryResp;
import com.qinglusaas.connect.infra.remote.saas.client.SaasClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasPriceClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasStoreClient;
import com.qinglusaas.connect.infra.remote.saas.client.SaasVehicleClient;
import com.qinglusaas.connect.infra.remote.saas.contants.BizErrorCode;
import com.qinglusaas.connect.infra.remote.saas.contants.IdRelationEnum;
import com.qinglusaas.connect.infra.remote.saas.contants.ServiceItemEnum;
import com.qinglusaas.connect.infra.remote.saas.contants.UserEnum;
import com.qinglusaas.connect.infra.remote.saas.dto.*;
import com.qinglusaas.connect.infra.remote.saas.vo.SaasResponse;
import com.qinglusaas.connect.infra.remote.saas.vo.request.*;
import com.qinglusaas.connect.infra.remote.saas.vo.response.*;
import com.qinglusaas.connect.infra.util.SpanUtil;
import com.qinglusaas.connect.infra.web.exception.NotFoundException;
import com.ql.dto.ApiResultResp;
import com.ql.dto.msg.PlatformSyncParam;
import com.ql.dto.msg.PriceInfoReq;
import com.ql.dto.msg.StoreInfoReq;
import com.ql.dto.price.PriceDatePull;
import com.ql.dto.store.StoreHourlyChargeDTO;
import com.ql.dto.vehicle.VehicleBusyDTO;
import com.ql.dto.open.response.OpenProductPriceCalendarResp;
import com.ql.dto.price.BasePriceParam;
import com.ql.dto.vehicle.VehicleInfoDTO;
import com.ql.enums.FeeTypeEnum;
import com.ql.enums.PriceTypeEnum;
import com.ql.enums.open.ModificationBusiTypeEnum;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.arc.log.LoggerName;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.function.Function;

import static com.ql.Constant.ChannelId.HELLO;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
@ApplicationScoped
public class HelloBaseServiceImpl extends AbsConsumer implements IHelloBaseService {

    @LoggerName(LogName.HELLO)
    Logger logger;
    @Inject
    StoreInfoRepository storeInfoRepository;
    @Inject
    AreaRepository areaRepository;
    @Inject
    VehicleBindRepository vehicleBindRepository;
    @Inject
    ModelUtil modelUtil;
    @RestClient
    SaasClient saasClient;
    @RestClient
    SaasStoreClient saasStoreClient;
    @RestClient
    SaasVehicleClient saasVehicleClient;
    @RestClient
    SaasPriceClient saasPriceClient;
    @Inject
    HelloClientWrapper helloClientWrapper;
    @Inject
    SaasFieldConverter saasFieldConverter;
    @Inject
    OrderMapper orderMapper;
    @Inject
    IVehicleCommonService vehicleCommonService;

    @Inject
    ThirdIdRelationRepository thirdIdRelationRepository;
    @Inject
    ThirdPlatformSourceInfoRepository thirdPlatformSourceInfoRepository;

    @Inject
    ApiConnRepository apiConnRepository;

    @Inject
    ThirdVehicleIdRelationRepository thirdVehicleIdRelationRepository;

    @Inject
    IHelloVehicleService helloVehicleService;

    @Inject
    HelloStoreServiceImpl helloStoreService;

    private final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(5);

    @ConfigProperty(name = "deployment.env", defaultValue = "prod")
    public String env;

    @Override
    public List<City> cities(Long merchantId) {
        // 获取所有门店的所属城市信息
        List<StoreInfoEntity> storeInfoEntities = storeInfoRepository.findAllStoreInfoByMerchantIdAndCityIds(merchantId,
                null);
        // 通过城市Code拿到所有门店，查询门店服务包是否支持上门取车服务（in操作）
        if (!storeInfoEntities.isEmpty()) {
            logger.infov("渠道[{0}]的门店信息, 共获取数据{1}条", merchantId, storeInfoEntities.size());
            List<Long> cityIds = storeInfoEntities.stream().map(StoreInfoEntity::getCityId)
                    .collect(Collectors.toList());
            List<AreaEntity> areaEntities = areaRepository.findAllByIds(cityIds);
            logger.infov("渠道[{0}]包含门店的城市列表:{1}", merchantId, areaEntities.size());
            Map<Long, List<StoreInfoEntity>> groupStoreInfo = storeInfoEntities.stream()
                    .collect(Collectors.groupingBy(StoreInfoEntity::getCityId));
            List<City> finalCities = new ArrayList<>();
            groupStoreInfo.keySet().forEach(k -> {
                AreaEntity areaEntity = areaEntities.stream().filter(entity -> entity.id.equals(k.longValue()))
                        .findFirst().orElse(null);
                if (null != areaEntity) {
                    City city = modelUtil.city(areaEntity, false);
                    finalCities.add(city);
                }
            });
            // 清理一下空数据
            logger.infov("渠道[{0}]包含门店送车服务的城市列表:{1}", merchantId, finalCities);
            return finalCities;
        } else {
            logger.infov("渠道[{0}]的城市门店数据为空", merchantId);
        }
        return Collections.EMPTY_LIST;
    }

    @Override
    public List<VehicleModelPriceDTO> getVehiclePriceList(Long merchantId, Long channelId, GetVehiclePriceListReq req) {
        PointDTO pickUpPoint = new PointDTO(Double.parseDouble(req.getPickUpRental().getLongitude()),
                Double.parseDouble(req.getPickUpRental().getLatitude()));
        PointDTO returnPoint = new PointDTO(Double.parseDouble(req.getDropOffRental().getLongitude()),
                Double.parseDouble(req.getDropOffRental().getLatitude()));
        // 查询City信息
        Set<String> phoneCityCodes = new HashSet<>();
        phoneCityCodes.add(req.getPickUpRental().getCityCode());
        phoneCityCodes.add(req.getDropOffRental().getCityCode());
        List<AreaEntity> areaEntities = areaRepository.findAllByPhoneCityCodesAndDepth(phoneCityCodes, 2);
        if (null == areaEntities || areaEntities.isEmpty()) {
            logger.warnv("hello 城市信息查询失败code {0}", areaEntities);
            return Lists.newArrayList();
        }
        Map<String, Long> areaMap = areaEntities.stream()
                .collect(Collectors.toMap(AreaEntity::getPhoneCityCode, AreaEntity::getId));
        // 查询门店
        StoreSearchReq storeSearchReq = new StoreSearchReq();
        storeSearchReq.setChannelId(channelId);
        storeSearchReq.setPickUpPoint(pickUpPoint);
        storeSearchReq.setReturnPoint(returnPoint);
        storeSearchReq.setPickUpCityId(Optional.ofNullable(areaMap.get(req.getPickUpRental().getCityCode()))
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_CITY.getCode(),
                        ErrorCode.NOT_FOUND_CITY.getDesc())));
        storeSearchReq.setReturnCityId(Optional.ofNullable(areaMap.get(req.getDropOffRental().getCityCode()))
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_CITY.getCode(),
                        ErrorCode.NOT_FOUND_CITY.getDesc())));
        storeSearchReq.setPickUpDate(req.getPickUpRental().getDatetime());
        storeSearchReq.setReturnDate(req.getDropOffRental().getDatetime());
        StoreSearchResp storeSearchResp = saasStoreClient.storeSearch(merchantId, storeSearchReq).getData();
        if (null == storeSearchResp.getStorePairList() || storeSearchResp.getStorePairList().isEmpty()) {
            return Lists.newArrayList();
        }
        // 取得门店取消规则
        CancelPolicyDTO cancelPolicyDTO = saasStoreClient
                .cancelPolicySearch(merchantId, req.getPickUpRental().getDatetime()).getData();

        // 转换成抽象门店查询
        StoreVehicleModelPriceReq saasReq = new StoreVehicleModelPriceReq();
        saasReq.setChannelId(channelId);
        AtomicLong fakeId = new AtomicLong(0);
        List<StorePairDTO> storePairDTOS = storeSearchResp.getStorePairList().stream()
                .map(storePairWithCircleDTO -> new StorePairDTO(fakeId.incrementAndGet(),
                        storePairWithCircleDTO.getPickUpStore().getStoreCircleIdDTO(),
                        storePairWithCircleDTO.getReturnStore().getStoreCircleIdDTO()))
                .collect(Collectors.toList());
        saasReq.setStorePairList(storePairDTOS);
        saasReq.setPickUpDate(req.getPickUpRental().getDatetime());
        saasReq.setReturnDate(req.getDropOffRental().getDatetime());
        saasReq.setPickUpPoint(pickUpPoint);
        saasReq.setReturnPoint(returnPoint);
        saasReq.setUseServiceCircle(1);
        saasReq.setOriginReq(req);
        StoreVehicleModelPriceResp response = saasVehicleClient.storeVehicleModelPriceSearch(merchantId, saasReq)
                .getData();
        if (null == response || null == response.getStorePairVehicleModelList()
                || response.getStorePairVehicleModelList().isEmpty()) {
            return Lists.newArrayList();
        }

        Set<Long> vehicleModelIds = response.getStorePairVehicleModelList().stream()
                .flatMap(storePairVehicleModelDTO -> storePairVehicleModelDTO.getVehicleModelPriceList().stream())
                .map(vehicleModelPriceDTO -> vehicleModelPriceDTO.getVehicleModelId())
                .collect(Collectors.toSet());
        // 查询车辆绑定信息
        List<VehicleBindEntity> vehicleBindEntities = vehicleBindRepository
                .findAllByVehicleModelIdsAndChannelId(vehicleModelIds, channelId.longValue());
        // 查询车辆tag
        Map<Long, List<String>> vehicleModelTagMap = vehicleCommonService.getVehicleModelTagMapping(response);

        // 车型map绑定 目前车型需要多对一匹配
        Map<Long, List<String>> vehicleBindMap = new HashMap<>();
        vehicleBindEntities.stream()
                .filter(entity -> channelId == entity.getChannelId())
                .forEach(entity -> {
                    if (!vehicleBindMap.containsKey(entity.getVehicleModelId())) {
                        vehicleBindMap.put(entity.getVehicleModelId(), new ArrayList<>());
                    }
                    vehicleBindMap.get(entity.getVehicleModelId()).add(entity.getBindChannelVehicleId());
                });
        // 服务项绑定code
        Map<String, String> serviceItemCodeMap = ServiceItemEnum.HelloMapping.getBackwardMapping();

        StoreVehicleModelPriceAggregate aggregate = new StoreVehicleModelPriceAggregate(merchantId, serviceItemCodeMap,
                response, vehicleBindEntities, vehicleModelTagMap, cancelPolicyDTO);

        List<VehicleModelPriceDTO> vehicleModelPriceDTOS = FromStoreVehicleModelPriceConverter.forward(aggregate);
        // 增加价格key platform-objHash-timestamp
        // String masterKey = String.format("%s-%s-%s", REDIS_PLATFORM_KEY,
        // System.identityHashCode(req), System.currentTimeMillis());
        // AtomicInteger idx = new AtomicInteger(0);
        // Map<Integer, VehicleModelPriceCache> priceCacheMap = new HashMap<>();
        // vehicleModelPriceDTOS.forEach(dto -> {
        // int subKey = idx.getAndIncrement();
        // VehicleModelPriceCache cache = new VehicleModelPriceCache();
        // cache.setPickUpRental(dto.getPickUpRental());
        // cache.setDropOffRental(dto.getDropOffRental());
        // cache.setServiceTagList(dto.getServiceTagList());
        // dto.setPriceKey(String.format("%s:%s", masterKey, subKey));

        // priceCacheMap.put(subKey, cache);
        // });
        // hashCommands.hset(masterKey, priceCacheMap);
        // // 5分钟失效
        // keyCommands.expire(masterKey, Duration.ofMinutes(5));
        return vehicleModelPriceDTOS;
    }

    @Override
    public GetVehiclePriceDetailResp getVehiclePriceDetail(Long merchantId, Long channelId,
            GetVehiclePriceDetailReq req) {
        // 查询车型绑定信息
        GetVehiclePriceDetailResp resp = null;
        try {
            Optional<VehicleBindEntity> vehicleBindEntityOpt = vehicleBindRepository
                    .findByBindChannelVehicleIdAndChannel(req.getVehicleCode(), channelId);
            if (vehicleBindEntityOpt.isEmpty()) {
                logger.warnv("车型绑定: {0} 不存在", req.getVehicleCode());
            }
            Long vehicleModelId = vehicleBindEntityOpt
                    .map(VehicleBindEntity::getVehicleModelId)
                    .orElseThrow(() -> new NotFoundException(
                            String.format("vehicleMode %s not found", req.getVehicleCode())));

            String companyCode = String.valueOf(merchantId);
            // 查询门店
            StorePairDTO storePairDTO = this.getStorePairDTOByParams(merchantId, channelId, req.getPickUpRental(),
                    req.getDropOffRental());

            VehicleModelPriceDetailReq remoteReq = new VehicleModelPriceDetailReq();
            remoteReq.setChannelId(channelId);
            remoteReq.setPickUpDate(req.getPickUpRental().getDatetime());
            remoteReq.setReturnDate(req.getDropOffRental().getDatetime());
            remoteReq.setStorePair(storePairDTO);
            remoteReq.setCouponCodeList(req.getCouponCodeList());

            PointDTO pickUpPoint = new PointDTO(Double.parseDouble(req.getPickUpRental().getLongitude()),
                    Double.parseDouble(req.getPickUpRental().getLatitude()));
            PointDTO returnPoint = new PointDTO(Double.parseDouble(req.getDropOffRental().getLongitude()),
                    Double.parseDouble(req.getDropOffRental().getLatitude()));
            remoteReq.setPickUpPoint(pickUpPoint);
            remoteReq.setReturnPoint(returnPoint);
            // 携程一定使用服务圈
            remoteReq.setUseServiceCircle(1);
            remoteReq.setOriginReq(req);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            LocalDateTime pickUpLocalDateTime = LocalDateTime.parse(req.getPickUpRental().getDatetime(), formatter);
            VehicleModelPriceDetailResp vehicleModelPriceDetailResp = saasVehicleClient
                    .vehicleModelPriceDetail(merchantId, vehicleModelId, remoteReq).getData();
            if (null == vehicleModelPriceDetailResp) {
                logger.warn("saas 接口熔断");
                throw new BizException(ClientName.HELLO,
                        com.qinglusaas.connect.client.hello.error.BizErrorCode.STOCK_OUT.getCode(),
                        com.qinglusaas.connect.client.hello.error.BizErrorCode.STOCK_OUT.getDesc(),
                        BizErrorCode.CIRCUIT_BREAKER_OPEN.getCode(), BizErrorCode.CIRCUIT_BREAKER_OPEN.getDesc());
            }
            // 取的区域信息
            Map<Long, AreaEntity> areaEntityMap = new HashMap<>();
            if (null != vehicleModelPriceDetailResp.getServicePolicy()
                    && null != vehicleModelPriceDetailResp.getServicePolicy().getProhibitedArea()) {
                ServicePolicyDTO.ProhibitedAreaVO prohibitedArea = vehicleModelPriceDetailResp.getServicePolicy()
                        .getProhibitedArea();
                Set<Long> areaIds = new HashSet<>();
                Optional.ofNullable(prohibitedArea.getProhibitIntoAreaIdList())
                        .ifPresent(areas -> areas.forEach(areaIds::add));
                Optional.ofNullable(prohibitedArea.getProhibitOutAreaIdList())
                        .ifPresent(areas -> areas.forEach(areaIds::add));
                if (!areaIds.isEmpty()) {
                    areaRepository.findAllByIds(areaIds)
                            .forEach(areaEntity -> areaEntityMap.put(areaEntity.getId(), areaEntity));
                }
            }
            VehicleModelPriceDetailAggregate aggregate = new VehicleModelPriceDetailAggregate(
                    ServiceItemEnum.HelloMapping.getBackwardMapping(),
                    vehicleModelPriceDetailResp, null, req.getVehicleCode(), pickUpLocalDateTime, areaEntityMap);

            resp = FromVehicleModelPriceDetailConverter.forward(aggregate);
            // 回填门店数据
            resp.setPickUpRental(StoreRentalDTO.instanceOf(companyCode, storePairDTO.getPickUpStore().getPhones(),
                    req.getPickUpRental()));
            resp.setDropOffRental(StoreRentalDTO.instanceOf(companyCode, storePairDTO.getPickUpStore().getPhones(),
                    req.getDropOffRental()));
        } catch (Exception e) {
            logger.error("价格明细接口异常", e);
            throw new RuntimeException(e);
        }
        return resp;
    }

    @WithSpan("校验创建订单")
    @Override
    public void validateCreateOrder(@SpanAttribute("渠道ID") Long channelId, @SpanAttribute("商户ID") Long merchantId,
            PreBookingReq req) {
        Span span = Span.current();
        ValidateCreateOrderReq validateOrderReq = new ValidateCreateOrderReq();
        convertOrderReq(merchantId, channelId, req, validateOrderReq, span);
        validateOrderReq.setChannelId(channelId);
        validateOrderReq.setOriginReq(req);
        SaasResponse saasResponse = saasClient.postCheckOrder(channelId, merchantId, validateOrderReq);
        if (saasResponse.isOk()) {
            span.setStatus(StatusCode.OK);
            span.end();
        } else {
            BizCodeErrorConverter converter = new BizCodeErrorConverter();
            com.qinglusaas.connect.client.hello.error.BizErrorCode bizErrorCode = converter
                    .convert(BizErrorCode.code(saasResponse.getCode()));
            throw SpanUtil.recordHelloBizException(span, saasResponse, ClientName.HELLO, bizErrorCode);
        }
    }

    @WithSpan("创建订单")
    @Override
    public Long createOrder(@SpanAttribute("渠道ID") Long channelId, @SpanAttribute("商户ID") Long merchantId,
            MakeBookingReq req) {
        Span span = Span.current();
        CreateOrderReq createOrderReq = new CreateOrderReq();
        convertOrderReq(merchantId, channelId, req, createOrderReq, span);
        // hello订单号
        createOrderReq.setThirdOrderId(req.getHelloOrderNo());
        createOrderReq.setFreeDepositDegree(saasFieldConverter.helloFreeDepositDegreeCovert(req.getCreditSupport()));
        // 只要不是不支持信用免押，默认都是芝麻双免
        if (createOrderReq.getFreeDepositDegree().getCode() != FreeDepositDegree.NO_SUPPORT.getCode()) {
            createOrderReq.setFreeDepositWay(FreeDepositWay.ZHIMA_RENT);
        }
        span.setAttribute("hello订单号", req.getHelloOrderNo());
        createOrderReq.setOriginReq(req);
        SaasResponse<CreateOrderRespV2> saasResponse = saasClient.postCreateOrderV2(channelId, merchantId,
                createOrderReq);
        if (saasResponse.isOk()) {
            span.setStatus(StatusCode.OK);
            Long orderId = saasResponse.getData().getOrderId();
            span.setAttribute("saas订单号", orderId);
            span.end();
            return orderId;
        } else {
            BizCodeErrorConverter converter = new BizCodeErrorConverter();
            com.qinglusaas.connect.client.hello.error.BizErrorCode bizErrorCode = converter
                    .convert(BizErrorCode.code(saasResponse.getCode()));
            throw SpanUtil.recordHelloBizException(span, saasResponse, ClientName.HELLO, bizErrorCode);
        }
    }

    @WithSpan("校验取消订单")
    @Override
    public ValidateCancelOrder validateCancelOrder(@SpanAttribute("渠道ID") Long channelId,
            @SpanAttribute("商户ID") Long merchantId, OrderCancelValidateReq orderCancelValidateReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", orderCancelValidateReq.getHelloOrderNo());
        span.setAttribute("saas订单号", orderCancelValidateReq.getOrderNo());
        ValidateCancelOrderReq checkCancelOrderReq = new ValidateCancelOrderReq();
        checkCancelOrderReq.setChannelId(channelId);
        checkCancelOrderReq.setOrderId(Long.parseLong(orderCancelValidateReq.getOrderNo()));
        checkCancelOrderReq.setThirdOrderId(orderCancelValidateReq.getHelloOrderNo());
        checkCancelOrderReq.setOriginReq(orderCancelValidateReq);
        SaasResponse<ValidateCancelOrderResp> saasResponse = saasClient.validateCancelOrder(channelId, merchantId,
                checkCancelOrderReq);
        if (saasResponse.isOk()) {
            ValidateCancelOrderResp checkCancelOrderResp = saasResponse.getData();
            ValidateCancelOrder validateCancelOrder = new ValidateCancelOrder();
            validateCancelOrder.setDeductAmount(checkCancelOrderResp.getDeductAmount());
            validateCancelOrder.setDeductRemark(checkCancelOrderResp.getDeductRemark());
            span.setAttribute("订单违约金备注", checkCancelOrderResp.getDeductRemark());
            span.setAttribute("订单违约金(分)", checkCancelOrderResp.getDeductAmount().getPrice());
            span.setStatus(StatusCode.OK);
            span.end();
            return validateCancelOrder;
        } else {
            throw SpanUtil.recordSaasBizException(span, saasResponse, ClientName.HELLO,
                    ErrorCode.VALIDATE_CANCEL_ORDER_ERROR);
        }
    }

    @WithSpan("取消订单")
    @Override
    public CancelOrder cancelOrder(@SpanAttribute("渠道ID") Long channelId, @SpanAttribute("商户ID") Long merchantId,
            OrderCancelReq orderCancelReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", orderCancelReq.getHelloOrderNo());
        span.setAttribute("saas订单号", orderCancelReq.getOrderNo());
        CancelOrderReq cancelOrderReq = new CancelOrderReq();
        cancelOrderReq.setChannelId(channelId);
        cancelOrderReq.setOrderId(Long.parseLong(orderCancelReq.getOrderNo()));
        cancelOrderReq.setThirdOrderId(orderCancelReq.getHelloOrderNo());
        cancelOrderReq.setOriginReq(orderCancelReq);
        SaasResponse<CancelOrderResp> saasResponse = saasClient.cancelOrder(channelId, merchantId, cancelOrderReq);
        if (saasResponse.isOk()) {
            CancelOrderResp cancelOrderResp = saasResponse.getData();
            CancelOrder cancelOrder = new CancelOrder();
            cancelOrder.setDeductAmount(cancelOrderResp.getDeductAmount());
            cancelOrder.setDeductRemark(cancelOrderResp.getDeductRemark());
            span.setAttribute("订单违约金备注", cancelOrderResp.getDeductRemark());
            span.setAttribute("订单违约金(分)", cancelOrderResp.getDeductAmount().getPrice());
            span.setStatus(StatusCode.OK);
            span.end();
            return cancelOrder;
        } else {
            throw SpanUtil.recordSaasBizException(span, saasResponse, ClientName.HELLO, ErrorCode.CANCEL_ORDER_ERROR);
        }
    }

    /**
     * 校验取消续租订单
     *
     * @param channelId
     * @param merchantId
     * @param renewOrderCancelValidateReq
     * @return
     */
    @WithSpan("校验取消续租订单")
    @Override
    public ValidateCancelRenewOrder validateCancelRenewOrder(@SpanAttribute("渠道ID") Long channelId,
            @SpanAttribute("商户ID") Long merchantId, RenewOrderCancelValidateReq renewOrderCancelValidateReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", renewOrderCancelValidateReq.getHelloOrderNo());
        span.setAttribute("hello续租订单号", renewOrderCancelValidateReq.getRenewOrderNo());
        span.setAttribute("saas订单号", renewOrderCancelValidateReq.getOrderNo());

        ValidateCancelRenewOrderReq validateCancelRenewOrderReq = new ValidateCancelRenewOrderReq();
        validateCancelRenewOrderReq.setChannelId(channelId);
        validateCancelRenewOrderReq.setRenewOrderId(Long.parseLong(renewOrderCancelValidateReq.getRenewOrderNo()));
        validateCancelRenewOrderReq.setOrderId(Long.parseLong(renewOrderCancelValidateReq.getOrderNo()));
        validateCancelRenewOrderReq.setThirdOrderId(renewOrderCancelValidateReq.getHelloOrderNo());
        validateCancelRenewOrderReq.setOriginReq(renewOrderCancelValidateReq);

        SaasResponse<ValidateCancelRenewOrderResp> saasResponse = saasClient.validateCancelRenewOrder(channelId,
                merchantId, validateCancelRenewOrderReq);
        if (saasResponse.isOk()) {
            ValidateCancelRenewOrderResp cancelRenewOrderValidateResp = saasResponse.getData();
            ValidateCancelRenewOrder cancelRenewOrderValidate = new ValidateCancelRenewOrder();
            cancelRenewOrderValidate.setDeductAmount(cancelRenewOrderValidateResp.getDeductAmount());
            cancelRenewOrderValidate.setDeductRemark(cancelRenewOrderValidateResp.getDeductRemark());
            span.setAttribute("取消续租违约金备注", cancelRenewOrderValidate.getDeductRemark());
            span.setAttribute("取消续租违约金(分)", cancelRenewOrderValidate.getDeductAmount().getPrice());
            span.setStatus(StatusCode.OK);
            span.end();
            return cancelRenewOrderValidate;
        } else {
            throw SpanUtil.recordSaasBizException(span, saasResponse, ClientName.HELLO,
                    ErrorCode.VALIDATE_RENEW_ORDER_ERROR);
        }
    }

    /**
     * 取消续租订单
     *
     * @param channelId
     * @param merchantId
     * @param renewOrderCancelReq
     * @return
     */
    @WithSpan("取消续租订单")
    @Override
    public CancelRenewOrder cancelRenewOrder(@SpanAttribute("渠道ID") Long channelId,
            @SpanAttribute("商户ID") Long merchantId, RenewOrderCancelReq renewOrderCancelReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", renewOrderCancelReq.getHelloOrderNo());
        span.setAttribute("hello续租订单号", renewOrderCancelReq.getHelloRenewOrderNo());
        span.setAttribute("saas订单号", renewOrderCancelReq.getOrderNo());
        span.setAttribute("saas续租订单号", renewOrderCancelReq.getRenewOrderNo());

        CancelRenewOrderReq cancelRenewOrderReq = new CancelRenewOrderReq();
        cancelRenewOrderReq.setChannelId(channelId);
        cancelRenewOrderReq.setRenewOrderId(Long.parseLong(renewOrderCancelReq.getRenewOrderNo()));
        cancelRenewOrderReq.setOrderId(Long.parseLong(renewOrderCancelReq.getOrderNo()));
        cancelRenewOrderReq.setThirdOrderId(renewOrderCancelReq.getHelloOrderNo());
        cancelRenewOrderReq.setThirdRenewOrderId(renewOrderCancelReq.getHelloRenewOrderNo());
        cancelRenewOrderReq.setOriginReq(renewOrderCancelReq);

        SaasResponse<CancelRenewOrderResp> saasResponse = saasClient.cancelRenewOrder(channelId, merchantId,
                cancelRenewOrderReq);
        if (saasResponse.isOk()) {
            CancelRenewOrderResp cancelRenewOrderResp = saasResponse.getData();
            CancelRenewOrder cancelRenewOrder = new CancelRenewOrder();
            cancelRenewOrder.setDeductAmount(cancelRenewOrderResp.getDeductAmount());
            cancelRenewOrder.setDeductRemark(cancelRenewOrderResp.getDeductRemark());
            span.setAttribute("取消租续违约金(分)", cancelRenewOrder.getDeductAmount().getPrice());
            span.setAttribute("取消租续违约金备注", cancelRenewOrder.getDeductRemark());
            span.setStatus(StatusCode.OK);
            span.end();
            return cancelRenewOrder;
        } else {
            throw SpanUtil.recordSaasBizException(span, saasResponse, ClientName.HELLO,
                    ErrorCode.CANCEL_RENEW_ORDER_ERROR);
        }
    }

    /**
     * 校验续租订单
     *
     * @param channelId
     * @param merchantId
     * @param preRenewBookingReq
     * @return
     */
    @WithSpan("校验续租订单")
    @Override
    public ValidateCreateRenewOrder validateCreateRenewOrder(@SpanAttribute("渠道ID") Long channelId,
            @SpanAttribute("商户ID") Long merchantId, PreRenewBookingReq preRenewBookingReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", preRenewBookingReq.getHelloOrderNo());
        span.setAttribute("saas订单号", preRenewBookingReq.getOrderNo());
        span.setAttribute("saas续租开始时间", DateTimeUtil.timeFormat(preRenewBookingReq.getRenewStartTime().getTime()));
        span.setAttribute("saas续租结束时间", DateTimeUtil.timeFormat(preRenewBookingReq.getRenewEndTime().getTime()));

        ValidateCreateRenewOrderReq validateCreateRenewOrderReq = new ValidateCreateRenewOrderReq();
        validateCreateRenewOrderReq.setChannelId(channelId);
        validateCreateRenewOrderReq.setRenewStartTime(preRenewBookingReq.getRenewStartTime());
        validateCreateRenewOrderReq.setRenewEndTime(preRenewBookingReq.getRenewEndTime());
        validateCreateRenewOrderReq.setOrderId(Long.parseLong(preRenewBookingReq.getOrderNo()));
        validateCreateRenewOrderReq.setThirdOrderId(preRenewBookingReq.getHelloOrderNo());
        validateCreateRenewOrderReq.setOriginReq(preRenewBookingReq);

        SaasResponse<ValidateCreateRenewOrderResp> saasResponse = saasClient.validateCreateRenewOrder(channelId,
                merchantId, validateCreateRenewOrderReq);
        if (saasResponse.isOk()) {
            ValidateCreateRenewOrderResp validateCreateRenewOrderResp = saasResponse.getData();
            span.setAttribute("价格一致码", validateCreateRenewOrderResp.getPriceKey());
            span.setAttribute("费用项总价(分)", validateCreateRenewOrderResp.getFeeItemTotalAmount().getPrice());
            span.setAttribute("费用项", SpanUtil.toJson(validateCreateRenewOrderResp.getFeeItems()));
            span.setStatus(StatusCode.OK);
            span.end();
            return orderMapper.toValidateRenewOrder(validateCreateRenewOrderResp);
        } else {
            BizCodeErrorConverter converter = new BizCodeErrorConverter();
            com.qinglusaas.connect.client.hello.error.BizErrorCode bizErrorCode = converter
                    .convert(BizErrorCode.code(saasResponse.getCode()));
            throw SpanUtil.recordHelloBizException(span, saasResponse, ClientName.HELLO, bizErrorCode);
        }
    }

    /**
     * 创建续租订单
     *
     * @param channelId
     * @param merchantId
     * @param makeRenewBookingReq
     * @return
     */
    @WithSpan("创建续租订单")
    @Override
    public CreateRenewOrder createRenewOrder(@SpanAttribute("渠道ID") Long channelId,
            @SpanAttribute("商户ID") Long merchantId, MakeRenewBookingReq makeRenewBookingReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", makeRenewBookingReq.getHelloOrderNo());
        span.setAttribute("hello续租订单号", makeRenewBookingReq.getHelloRenewOrderNo());
        span.setAttribute("saas订单号", makeRenewBookingReq.getOrderNo());
        span.setAttribute("续租开始时间", DateTimeUtil.timeFormat(makeRenewBookingReq.getRenewStartTime().getTime()));
        span.setAttribute("续租结束时间", DateTimeUtil.timeFormat(makeRenewBookingReq.getRenewEndTime().getTime()));
        span.setAttribute("价格一致码", makeRenewBookingReq.getPriceKey());
        span.setAttribute("续租支付总价（分）", makeRenewBookingReq.getTotalAmount());

        CreateRenewOrderReq createRenewOrderReq = new CreateRenewOrderReq();
        createRenewOrderReq.setChannelId(channelId);
        createRenewOrderReq.setThirdOrderId(makeRenewBookingReq.getHelloOrderNo());
        createRenewOrderReq.setThirdRenewOrderId(makeRenewBookingReq.getHelloRenewOrderNo());
        createRenewOrderReq.setRenewEndTime(makeRenewBookingReq.getRenewEndTime());
        createRenewOrderReq.setRenewStartTime(makeRenewBookingReq.getRenewStartTime());
        createRenewOrderReq.setPriceKey(makeRenewBookingReq.getPriceKey());
        createRenewOrderReq.setTotalAmount(makeRenewBookingReq.getTotalAmount());
        createRenewOrderReq.setOriginReq(makeRenewBookingReq);

        SaasResponse<CreateRenewOrderResp> saasResponse = saasClient.createRenewOrder(channelId, merchantId,
                createRenewOrderReq);
        if (saasResponse.isOk()) {
            CreateRenewOrder createRenewOrder = new CreateRenewOrder();
            CreateRenewOrderResp createRenewOrderResp = saasResponse.getData();
            createRenewOrder.setOrderId(createRenewOrderResp.getOrderId());
            createRenewOrder.setRenewOrderId(createRenewOrderResp.getRenewOrderId());
            createRenewOrder.setThirdOrderId(createRenewOrderResp.getThirdOrderId());
            createRenewOrder.setThirdRenewOrderId(createRenewOrderResp.getThirdRenewOrderId());
            createRenewOrder.setTotalAmount(createRenewOrderResp.getTotalAmount());
            span.setAttribute("saas续租订单号", createRenewOrder.getRenewOrderId());
            span.setStatus(StatusCode.OK);
            span.end();
            return createRenewOrder;
        } else {
            BizCodeErrorConverter converter = new BizCodeErrorConverter();
            com.qinglusaas.connect.client.hello.error.BizErrorCode bizErrorCode = converter
                    .convert(BizErrorCode.code(saasResponse.getCode()));
            throw SpanUtil.recordHelloBizException(span, saasResponse, ClientName.HELLO, bizErrorCode);
        }
    }

    /**
     * 组装订单请求类
     *
     * @param merchantId
     * @param channelId
     * @param req
     * @param span
     * @return
     */
    private ValidateCreateOrderReq convertOrderReq(Long merchantId, Long channelId, PreBookingReq req,
            ValidateCreateOrderReq orderReq, Span span) {
        Optional<VehicleBindEntity> vehicleBindEntityOptional = vehicleBindRepository
                .findByBindChannelVehicleIdAndChannel(req.getVehicleCode(), channelId);
        if (vehicleBindEntityOptional.isPresent()) {
            VehicleBindEntity vehicleBindEntity = vehicleBindEntityOptional.get();
            orderReq.setVehicleModelId(vehicleBindEntity.getVehicleModelId());
            span.setAttribute("车型ID", vehicleBindEntity.getVehicleModelId());
        } else {
            Attributes attributes = Attributes.builder().put("请求报文", SpanUtil.toJson(req)).build();
            throw SpanUtil.recordSaasBizException(span, null, ClientName.HELLO, ErrorCode.NOT_FOUND_VEHICLE,
                    attributes);
        }
        orderReq.setPayAmount(req.getAmount());
        span.setAttribute("实收金额(分)", orderReq.getPayAmount());
        orderReq.setReceivableAmount(req.getAmount());
        span.setAttribute("应收金额(分)", orderReq.getPayAmount());
        // 车型报价
        orderReq.setPriceKey(req.getPriceKey());
        span.setAttribute("车型报价一致码", orderReq.getPriceKey());

        // 取车信息
        List<AreaEntity> pickUpCity = areaRepository
                .findAllByPhoneCityCodesAndDepth(Lists.newArrayList(req.getPickUpRental().getCityCode()), 2);
        if (null == pickUpCity || pickUpCity.isEmpty()) {
            logger.errorv("hello 城市信息查询失败code {0}", req.getPickUpRental().getCityCode());
            throw SpanUtil.recordSaasBizException(span, null, ClientName.HELLO, ErrorCode.NOT_FOUND_CITY);
        }

        PickUpStoreDTO pickUpStoreDTO = new PickUpStoreDTO();
        pickUpStoreDTO.setAddress(req.getPickUpRental().getAddress());
        pickUpStoreDTO.setStoreId(Long.parseLong(req.getPickUpRental().getStoreCode()));
        pickUpStoreDTO
                .setDatetime(DateTimeUtil.formatToDate(req.getPickUpRental().getDatetime(), DateTimeUtil.PATTERN_M));
        pickUpStoreDTO.setCityId(pickUpCity.get(0).getId());
        pickUpStoreDTO.setPickUpDropOffType(PickUpDropOffType.fromCode(
                saasFieldConverter.helloPickUpDropOffTypeCovert(req.getPickUpRental().getPickUpDropOffType())));
        com.qinglusaas.connect.client.saas.dto.PointDTO pickUpStorePointDTO = new com.qinglusaas.connect.client.saas.dto.PointDTO();
        Optional.ofNullable(req.getPickUpRental()).ifPresent(pickUpRental -> {
            pickUpStorePointDTO.setLongitude(Double.parseDouble(pickUpRental.getLongitude()));
            pickUpStorePointDTO.setLatitude(Double.parseDouble(pickUpRental.getLatitude()));
        });
        pickUpStoreDTO.setPoint(pickUpStorePointDTO);
        span.setAttribute("取车信息", SpanUtil.toJson(pickUpStoreDTO));
        orderReq.setPickUpStore(pickUpStoreDTO);

        // 还车信息
        List<AreaEntity> dropOffCity = areaRepository
                .findAllByPhoneCityCodesAndDepth(Lists.newArrayList(req.getDropOffRental().getCityCode()), 2);
        if (null == dropOffCity || dropOffCity.isEmpty()) {
            logger.errorv("hello 城市信息查询失败code {0}", req.getDropOffRental().getCityCode());
            throw SpanUtil.recordSaasBizException(span, null, ClientName.HELLO, ErrorCode.NOT_FOUND_CITY);
        }

        DropOffStoreDTO dropOffStoreDTO = new DropOffStoreDTO();
        dropOffStoreDTO.setAddress(req.getDropOffRental().getAddress());
        dropOffStoreDTO.setStoreId(Long.parseLong(req.getDropOffRental().getStoreCode()));
        dropOffStoreDTO
                .setDatetime(DateTimeUtil.formatToDate(req.getDropOffRental().getDatetime(), DateTimeUtil.PATTERN_M));
        dropOffStoreDTO.setCityId(dropOffCity.get(0).getId());
        dropOffStoreDTO.setPickUpDropOffType(PickUpDropOffType.fromCode(
                saasFieldConverter.helloPickUpDropOffTypeCovert(req.getDropOffRental().getPickUpDropOffType())));
        com.qinglusaas.connect.client.saas.dto.PointDTO dropOffStorePointDTO = new com.qinglusaas.connect.client.saas.dto.PointDTO();
        Optional.ofNullable(req.getDropOffRental()).ifPresent(rentalDTO -> {
            dropOffStorePointDTO.setLongitude(Double.parseDouble(rentalDTO.getLongitude()));
            dropOffStorePointDTO.setLatitude(Double.parseDouble(rentalDTO.getLatitude()));
        });
        dropOffStoreDTO.setPoint(dropOffStorePointDTO);
        span.setAttribute("还车信息", SpanUtil.toJson(dropOffStoreDTO));
        orderReq.setDropOffStore(dropOffStoreDTO);

        // 附加服务项
        List<String> addedServiceList = req.getAddedServices();
        if (null != addedServiceList) {
            List<String> addedServices = new ArrayList<>();
            addedServiceList.stream().forEach(s -> {
                String code = ServiceItemEnum.CtripMapping.getForwardMapping().get(s);
                if (null != code) {
                    addedServices.add(code);
                } else {
                    // 未识别服务项，可能会影响算价，所以在这里先抛异常，不允许下单，这种情况通常是服务项枚举不一致的原因导致
                    logger.warnv("未识别的附加服务项 {0}", s);
                    Attributes attributes = Attributes.builder().put("原始附加服务项", s).put("请求报文", SpanUtil.toJson(req))
                            .build();
                    throw SpanUtil.recordSaasBizException(span, null, ClientName.HELLO,
                            ErrorCode.NOT_FOUND_SERVICE_ITEM, attributes);
                }
            });
            orderReq.setAddedServices(addedServices);
            span.setAttribute("附加服务项", addedServiceList.stream().collect(Collectors.joining(",")));
        } else {
            span.setAttribute("附加服务项", "无");
        }
        DriverInfoDTO driverInfo = req.getDriverInfo();
        UserDTO user = new UserDTO();
        user.setName(driverInfo.getName());
        user.setMobile(driverInfo.getPhone());
        user.setIdNo(driverInfo.getCardNo());
        if (driverInfo.getCardType().equals(1)) {
            user.setIdType(UserEnum.IdTypeEnum.ID_CARD);
        } else if (driverInfo.getCardType().equals(2)) {
            user.setIdType(UserEnum.IdTypeEnum.PASSPORT);
        } else if (driverInfo.getCardType().equals(3)) {
            user.setIdType(UserEnum.IdTypeEnum.RETURN_HOME);
        } else if (driverInfo.getCardType().equals(4)) {
            user.setIdType(UserEnum.IdTypeEnum.TAIWAN);
        }
        span.setAttribute("下单用户", SpanUtil.toJson(user));
        // 个人信息
        orderReq.setPartnerUser(user);

        // 取还车服务圈
        StorePairDTO storePairDTO = getStorePairDTOByParams(merchantId, channelId, req.getPickUpRental(),
                req.getDropOffRental());
        if (null != storePairDTO) {
            StoreCircleIdDTO pickUpStoreCircle = storePairDTO.getPickUpStore();
            if (null != pickUpStoreCircle) {
                List<Long> Ids = pickUpStoreCircle.getServiceCircleIdList();
                span.setAttribute("取车门店服务圈ID集合", SpanUtil.toJson(Ids));
                orderReq.getPickUpStore().setServiceCircleIds(Ids);
            }
            StoreCircleIdDTO dropOffStoreCircle = storePairDTO.getReturnStore();
            if (null != dropOffStoreCircle) {
                List<Long> Ids = dropOffStoreCircle.getServiceCircleIdList();
                span.setAttribute("还车门店服务圈ID集合", SpanUtil.toJson(Ids));
                orderReq.getDropOffStore().setServiceCircleIds(Ids);
            }
        }
        return orderReq;
    }

    @WithSpan("订单状态回调")
    @Override
    public SaasResultResp callbackOrderStatus(CallbackOrderStatusReq callBackOrderStatusReq) {
        Span span = Span.current();
        span.setAttribute("hello订单号", callBackOrderStatusReq.getHelloOrderNo());
        span.setAttribute("saas订单号", callBackOrderStatusReq.getThirdOrderNo());
        span.setAttribute("违约金", callBackOrderStatusReq.getPenaltyAmount() != null ? callBackOrderStatusReq.getPenaltyAmount().toString() : "");
        span.setAttribute("取/还车时间", callBackOrderStatusReq.getPickUpDropOffTime());
        span.setAttribute("订单状态Code", callBackOrderStatusReq.getStatusCode().getCode());
        span.setAttribute("订单状态", callBackOrderStatusReq.getStatusCode().getDesc());
        ResultResp resultResp = helloClientWrapper.wrapperSignRequest(APIMethod.CALLBACK_ORDER_STATUS, callBackOrderStatusReq);
        if (resultResp.isSuccess()) {
            span.setStatus(StatusCode.OK);
            return SaasResultResp.success();
        } else {
            span.setStatus(StatusCode.ERROR);
            span.setAttribute("错误Code", resultResp.getCode());
            span.setAttribute("错误内容", resultResp.getMsg());
            span.setAttribute("子错误Code", resultResp.getSubCode());
            span.setAttribute("子错误内容", resultResp.getSubMsg());
            return SaasResultResp.failed("-1", resultResp.getMsg());
        }
    }

    @Override
    public GetOrderInfoResp getOrderInfo(Long channelId, Long merchantId, GetOrderInfoReq getOrderInfoReq) {
        GetOrderReq req = new GetOrderReq();
        req.setChannelId(channelId);
        req.setOrderId(Long.parseLong(getOrderInfoReq.getOrderNo()));
        req.setOriginReq(getOrderInfoReq);
        GetOrderResp resp = saasClient.getOrder(channelId, merchantId, req).getData();

        String vehicleCode = vehicleBindRepository.findByVehicleModelIdAndChannel(resp.getVehicleModelId(), channelId)
                .orElseThrow(() -> {
                    Attributes attributes = Attributes.builder()
                            .put("车型id", resp.getVehicleModelId())
                            .put("请求报文", SpanUtil.toJson(req))
                            .build();
                    return SpanUtil.recordSaasBizException(Span.current(), null, ClientName.HELLO,
                            ErrorCode.NOT_FOUND_VEHICLE, attributes);
                }).getBindChannelVehicleId();
        GetOrderAggregate aggregate = new GetOrderAggregate(resp, vehicleCode);

        return FromGetOrderReqConverter.forward(aggregate);
    }

    @Override
    public GetRenewOrderInfoResp getRenewOrderInfo(Long channelId, Long merchantId,
            GetRenewOrderInfoReq getRenewOrderInfoReq) {
        GetRenewalOrderReq req = new GetRenewalOrderReq();
        req.setChannelId(channelId);
        req.setOrderId(Long.parseLong(getRenewOrderInfoReq.getOrderNo()));
        req.setRenewalOrderId(Long.parseLong(getRenewOrderInfoReq.getRenewOrderNo()));
        req.setOriginReq(getRenewOrderInfoReq);
        GetRenewalOrderResp resp = saasClient.getRenewalOrder(channelId, merchantId, req).getData();

        String vehicleCode = vehicleBindRepository.findByVehicleModelIdAndChannel(resp.getVehicleModelId(), channelId)
                .orElseThrow(() -> {
                    Attributes attributes = Attributes.builder()
                            .put("车型id", resp.getVehicleModelId())
                            .put("请求报文", SpanUtil.toJson(req))
                            .build();
                    return SpanUtil.recordSaasBizException(Span.current(), null, ClientName.HELLO,
                            ErrorCode.NOT_FOUND_VEHICLE, attributes);
                }).getBindChannelVehicleId();
        GetRenewalOrderAggregate aggregate = new GetRenewalOrderAggregate(resp, vehicleCode);

        return FromGetRenewalOrderReqConverter.forward(aggregate);
    }

    @Override
    public SaasResultResp callbackRefund(CallbackReFundReq callbackReFundReq) {
        ResultResp resultResp = helloClientWrapper.wrapperSignRequest(APIMethod.CALLBACK_REFUND, callbackReFundReq);
        if (resultResp.isSuccess()) {
            return SaasResultResp.success(null);
        } else {
            throw new BizException(ClientName.HELLO, resultResp.getCode(), resultResp.getMsg());
        }
    }

    @Override
    public SaasResultResp callbackDeduct(CallbackDeductReq callbackDeductReq) {
        ResultResp resultResp = helloClientWrapper.wrapperSignRequest(APIMethod.CALLBACK_DEDUCT, callbackDeductReq);
        if (resultResp.isSuccess()) {
            return SaasResultResp.success();
        } else {
            throw new BizException(ClientName.HELLO, resultResp.getCode(), resultResp.getMsg());
        }
    }

    /**
     * 门店信息转换
     *
     * @param merchantId
     * @param channelId
     * @param pickUpRentalDTO
     * @param returnRentalDTO
     * @return
     */
    private StorePairDTO getStorePairDTOByParams(Long merchantId, Long channelId,
            RentalDTO pickUpRentalDTO, RentalDTO returnRentalDTO) {
        PointDTO pickUpPoint = PointDTO.fromStr(pickUpRentalDTO.getLongitude(), pickUpRentalDTO.getLatitude());
        PointDTO returnPoint = PointDTO.fromStr(returnRentalDTO.getLongitude(), returnRentalDTO.getLatitude());
        // 查询门店对应服务圈
        // 查询门店
        // 查询City信息
        Set<String> phoneCityCodes = new HashSet<>();
        phoneCityCodes.add(pickUpRentalDTO.getCityCode());
        phoneCityCodes.add(returnRentalDTO.getCityCode());
        List<AreaEntity> areaEntities = areaRepository.findAllByPhoneCityCodesAndDepth(phoneCityCodes, 2);
        if (null == areaEntities || areaEntities.isEmpty()) {
            logger.warnv("hello 城市信息查询失败code {0}", areaEntities);
            return null;
        }
        Map<String, Long> areaMap = areaEntities.stream()
                .collect(Collectors.toMap(AreaEntity::getPhoneCityCode, AreaEntity::getId));
        // 查询门店
        DeliveryServiceTypeConverter deliveryServiceTypeConverter = new DeliveryServiceTypeConverter();
        StoreSearchReq storeSearchReq = new StoreSearchReq();
        storeSearchReq.setChannelId(channelId);
        storeSearchReq.setPickUpStoreId(Long.parseLong(pickUpRentalDTO.getStoreCode()));
        storeSearchReq.setPickUpCityId(areaMap.get(pickUpRentalDTO.getCityCode()));
        storeSearchReq.setPickUpPoint(pickUpPoint);
        storeSearchReq.setPickUpDeliveryServiceType(
                deliveryServiceTypeConverter.reverse().convert(pickUpRentalDTO.getPickUpDropOffType()));
        storeSearchReq.setPickUpDate(pickUpRentalDTO.getDatetime());

        storeSearchReq.setReturnStoreId(Long.parseLong(returnRentalDTO.getStoreCode()));
        storeSearchReq.setReturnCityId(areaMap.get(returnRentalDTO.getCityCode()));
        storeSearchReq.setReturnPoint(returnPoint);
        storeSearchReq.setReturnDeliveryServiceType(
                deliveryServiceTypeConverter.reverse().convert(returnRentalDTO.getPickUpDropOffType()));
        storeSearchReq.setReturnDate(returnRentalDTO.getDatetime());

        StoreSearchResp storeSearchResp = saasStoreClient.storeSearch(merchantId, storeSearchReq).getData();
        if (null == storeSearchResp.getStorePairList() || storeSearchResp.getStorePairList().isEmpty()) {
            throw new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_STORE.getCode(),
                    ErrorCode.NOT_FOUND_STORE.getDesc());
        }

        StorePairWithCircleDTO storePairWithCircleDTO = storeSearchResp.getStorePairList().get(0);
        StoreCircleIdDTO pickUpStore = new StoreCircleIdDTO(storePairWithCircleDTO.getPickUpStore().getId(),
                storePairWithCircleDTO.getPickUpStore().getCityId(),
                storePairWithCircleDTO.getPickUpStore().getStoreCircleIdDTO().getServiceCircleIdList(),
                storePairWithCircleDTO.getPickUpStore().getPhones());
        StoreCircleIdDTO returnStore = new StoreCircleIdDTO(storePairWithCircleDTO.getReturnStore().getId(),
                storePairWithCircleDTO.getReturnStore().getCityId(),
                storePairWithCircleDTO.getReturnStore().getStoreCircleIdDTO().getServiceCircleIdList(),
                storePairWithCircleDTO.getReturnStore().getPhones());
        StorePairDTO storePairDTO = new StorePairDTO(1L, pickUpStore, returnStore);
        return storePairDTO;
    }

    @Override
    public SaasResultResp getVehicleModelList(QueryVehicleModelListReq queryVehicleModelListReq) {
        QueryVehicleModelListResultResp resultResp = helloClientWrapper
                .queryVehicleModelListRequest(APIMethod.QUERY_VEHICLE_MODEL_LIST, queryVehicleModelListReq);
        if (resultResp.isSuccess()) {
            return SaasResultResp.success(resultResp.getData());
        } else {
            throw new BizException(ClientName.HELLO, resultResp.getCode(), resultResp.getMsg());
        }
    }

    @Override
    public SaasResultResp<HelloGoodsInfoList> helloGoodsList(SaasGoodsReq saasReq, Long merchantId)
            throws Exception {
        Span span = Span.current();

        HelloGoodsListReq req = new HelloGoodsListReq();
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));
        req.setMerchantId(thirdMerchantId);
        if (Objects.nonNull(saasReq.getGoodsId())) {
            req.setGoodsIds(Collections.singletonList(saasReq.getGoodsId()));
        }
        ResultResp<HelloGoodsInfoList> goodsListResp = helloClientWrapper.goodsList(merchantId, req);
        if (Objects.isNull(goodsListResp) || !goodsListResp.isSuccess()) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            Attributes attributes = Attributes.builder()
                    .put("saasReq", objectMapper.writeValueAsString(saasReq))
                    .put("merchantId", merchantId)
                    .put("errorMsg", goodsListResp.getMsg())
                    .build();
            span.addEvent("调用hello商品详情接口 失败", attributes);
            throw new BizException(ClientName.HELLO, goodsListResp.getCode(), goodsListResp.getMsg());
        }
        return SaasResultResp.success(goodsListResp.getData());
    }

    @Override
    @WithSpan("[hello]库存新增消费")
    @Transactional
    public void stockAddNotify(PlatformSyncParam platformSyncParam) {
        Span span = Span.current();
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            span.addEvent("hello库存新增数据为空");
            return;
        }

        Long merchantId = platformSyncParam.getMerchantId();
        VehicleBusyDTO vehicleBusyDTO = this.deserialize(platformSyncParam.getData(), VehicleBusyDTO.class);
        if (vehicleBusyDTO == null) {
            return;
        }

        span.setAttribute("merchantId", merchantId);
        span.setAttribute("库存Id", vehicleBusyDTO.getId());
        span.setAttribute("车辆Id", vehicleBusyDTO.getVehicleId());
        span.setAttribute("startTime", vehicleBusyDTO.getStartTime());
        span.setAttribute("endIntervalTime", vehicleBusyDTO.getEndIntervalTime());

        // 获取第三方商户ID
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelIdOptional(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));

        // 获取第三方车辆ID
        String thirdVehicleId = getHelloVehicleMappingFromSaas(merchantId, vehicleBusyDTO.getVehicleId());
        if (thirdVehicleId == null) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("未找到对应的hello车辆id映射关系", Attributes.builder()
                    .put("vehicleId", vehicleBusyDTO.getVehicleId())
                    .put("merchantId", merchantId)
                    .build());
            return;
        }

        // 转换数据
        CarOccupyInventoryReq carOccupyInventoryReq = new HelloVehicleBusyConverter(thirdMerchantId, thirdVehicleId)
                .convert(vehicleBusyDTO);

        // 调用hello接口添加库存
        ResultResp<CarOccupyInventoryResp> resultResp = helloClientWrapper.occupyInventoryNotify(merchantId,
                carOccupyInventoryReq);
        if (!resultResp.isSuccess()) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("调用hello添加库存接口失败", Attributes.builder()
                    .put("errorCode", resultResp.getCode())
                    .put("errorMsg", resultResp.getMsg())
                    .build());
            return;
        }

        // 保存库存映射关系
        CarOccupyInventoryResp respData = resultResp.getData();
        if (respData != null && respData.getOccupyId() != null) {
            saveOrUpdateStockMapping(merchantId,
                    vehicleBusyDTO.getStoreId(),
                    vehicleBusyDTO.getId(),
                    String.valueOf(respData.getOccupyId()));
        }

        span.setStatus(StatusCode.OK);
        span.addEvent("库存新增消费成功");
    }

    @Override
    @WithSpan("[hello]库存更新消费")
    @Transactional
    public void stockUpdNotify(PlatformSyncParam platformSyncParam) {
        Span span = Span.current();
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            span.addEvent("hello库存更新数据为空");
            return;
        }

        Long merchantId = platformSyncParam.getMerchantId();
        VehicleBusyDTO vehicleBusyDTO = this.deserialize(platformSyncParam.getData(), VehicleBusyDTO.class);
        if (vehicleBusyDTO == null) {
            return;
        }

        span.setAttribute("merchantId", merchantId);
        span.setAttribute("库存Id", vehicleBusyDTO.getId());
        span.setAttribute("车辆Id", vehicleBusyDTO.getVehicleId());
        span.setAttribute("startTime", vehicleBusyDTO.getStartTime());
        span.setAttribute("endIntervalTime", vehicleBusyDTO.getEndIntervalTime());

        // 获取第三方商户ID
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelIdOptional(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));

        // 获取第三方车辆ID
        String thirdVehicleId = getHelloVehicleMappingFromSaas(merchantId, vehicleBusyDTO.getVehicleId());
        if (thirdVehicleId == null) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("未找到对应的hello车辆id映射关系", Attributes.builder()
                    .put("vehicleId", vehicleBusyDTO.getVehicleId())
                    .put("merchantId", merchantId)
                    .build());
            return;
        }

        // 1. 查询库存映射关系
        Optional<ThirdVehicleIdRelationEntity> stockMappingOpt = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.STOCK.getType(), vehicleBusyDTO.getId());

        if (stockMappingOpt.isEmpty()) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("未找到库存映射关系", Attributes.builder()
                    .put("stockId", vehicleBusyDTO.getId())
                    .put("merchantId", merchantId)
                    .build());
            return;
        }

        // 2. 释放原有库存
        CarReleaseInventoryReq releaseReq = new CarReleaseInventoryReq();
        releaseReq.setMerchantId(thirdMerchantId);
        releaseReq.setSiteId(helloStoreService.getHelloStoreMappingFromSaas(merchantId,
                IdRelationEnum.Store.STORE.getType(), vehicleBusyDTO.getStoreId()));
        releaseReq.setOccupyIdList(Collections.singletonList(Long.valueOf(stockMappingOpt.get().getThirdId())));
        releaseReq.setTemp(true);
        releaseReq.setCarId(thirdVehicleId);

        ResultResp<CarReleaseInventoryResp> releaseResp = releaseInventoryWithRetry(merchantId, releaseReq);
        if (!releaseResp.isSuccess() || !releaseResp.getData().getResult()) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("调用hello释放库存接口失败", Attributes.builder()
                    .put("errorCode", releaseResp.getCode())
                    .put("errorMsg", releaseResp.getMsg())
                    .build());
            return;
        }

        // 3. 重新占用库存
        CarOccupyInventoryReq occupyReq = new HelloVehicleBusyConverter(thirdMerchantId, thirdVehicleId)
                .convert(vehicleBusyDTO);

        ResultResp<CarOccupyInventoryResp> occupyResp = helloClientWrapper.occupyInventoryNotify(merchantId, occupyReq);
        if (!occupyResp.isSuccess()) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("调用hello添加库存接口失败", Attributes.builder()
                    .put("errorCode", occupyResp.getCode())
                    .put("errorMsg", occupyResp.getMsg())
                    .build());
            return;
        }

        // 4. 更新库存映射关系
        CarOccupyInventoryResp respData = occupyResp.getData();
        if (respData != null && respData.getOccupyId() != null) {
            saveOrUpdateStockMapping(merchantId,
                    vehicleBusyDTO.getStoreId(),
                    vehicleBusyDTO.getId(),
                    String.valueOf(respData.getOccupyId()));
        }

        span.setStatus(StatusCode.OK);
        span.addEvent("库存更新消费成功");
    }

    @Override
    @WithSpan("[hello]库存释放消费")
    @Transactional
    public void stockDeleteNotify(PlatformSyncParam platformSyncParam) {
        Span span = Span.current();
        if (platformSyncParam == null || platformSyncParam.getMerchantId() == null
                || Strings.isNullOrEmpty(platformSyncParam.getData())) {
            span.addEvent("库存释放消费数据为空");
            return;
        }

        Long merchantId = platformSyncParam.getMerchantId();
        VehicleBusyDTO vehicleBusyDTO = this.deserialize(platformSyncParam.getData(), VehicleBusyDTO.class);
        if (vehicleBusyDTO == null) {
            return;
        }

        span.setAttribute("merchantId", merchantId);
        span.setAttribute("库存Id", vehicleBusyDTO.getId());
        span.setAttribute("车辆Id", vehicleBusyDTO.getVehicleId());

        // 获取第三方商户ID
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelIdOptional(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));

        // 获取库存映射id
        ThirdVehicleIdRelationEntity entity = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.STOCK.getType(), vehicleBusyDTO.getId())
                .orElse(null);
        if (entity == null) {
            span.addEvent("未找到对应的hello库存id映射关系", Attributes.builder()
                    .put("merchantId", merchantId)
                    .put("stockId", vehicleBusyDTO.getId())
                    .build());
            return;
        }
        String thirdStockId = entity.getThirdId();
        String thirdVehicleId = getHelloVehicleMappingFromSaas(merchantId, vehicleBusyDTO.getVehicleId());

        // 构建释放库存请求
        CarReleaseInventoryReq releaseReq = new CarReleaseInventoryReq();
        releaseReq.setMerchantId(thirdMerchantId);
        releaseReq.setSiteId(helloStoreService.getHelloStoreMappingFromSaas(merchantId,
                IdRelationEnum.Store.STORE.getType(), vehicleBusyDTO.getStoreId()));
        releaseReq.setOccupyIdList(Collections.singletonList(Long.valueOf(thirdStockId)));
        releaseReq.setTemp(true);
        releaseReq.setCarId(thirdVehicleId);

        // 调用hello接口释放库存
        ResultResp<CarReleaseInventoryResp> resultResp = releaseInventoryWithRetry(merchantId, releaseReq);
        if (!resultResp.isSuccess() || !resultResp.getData().getResult()) {
            span.setStatus(StatusCode.ERROR);
            span.addEvent("调用hello释放库存接口失败", Attributes.builder()
                    .put("errorCode", resultResp.getCode())
                    .put("errorMsg", resultResp.getMsg())
                    .build());
            return;
        }

        // 删除库存映射关系
        deleteStockMapping(merchantId, vehicleBusyDTO.getId(), thirdStockId, entity);

        span.setStatus(StatusCode.OK);
        span.addEvent("库存释放消费成功");
    }

    /**
     * 哈啰基础数据导入
     * 入参未定义 @孙
     */
    @Override
    public SaasResultResp initHelloPricePull(VehicleInitReq param) {
        Long merchantId = param.getMerchantId();
        // 查询渠道是否已开通
        HelloGoodsListReq req = new HelloGoodsListReq();
        String helloMerchantId = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode).orElseThrow(() -> new BizException(ClientName.HELLO,
                        ErrorCode.NOT_FOUND_MERCHANT.getCode(), ErrorCode.NOT_FOUND_MERCHANT.getDesc()));

        // 查询哈啰关联门店及关联车型
        List<ThirdIdRelationEntity> storeList = thirdIdRelationRepository.findByChannelIdAndMerchantId(HELLO,
                merchantId, IdRelationEnum.Store.STORE.getType());
        if (storeList.isEmpty()) {
            this.initSuccessV2(merchantId, 60);
            // return;
        }
        Map<String, Long> storeMap = storeList.stream()
                .collect(Collectors.toMap(ThirdIdRelationEntity::getThirdId, ThirdIdRelationEntity::getSaasId));
        List<String> HelloStoreIdList = storeList.stream()
                .map(ThirdIdRelationEntity::getThirdId)
                .distinct()
                .collect(Collectors.toList());

        // 哈啰商品与Saas车型关系
        List<ThirdVehicleIdRelationEntity> modelList = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndType(HELLO, merchantId, IdRelationEnum.Vehicle.VEHICLE_MODEL.getType());
        if (modelList.isEmpty()) {
            this.initSuccessV2(merchantId, 60);
            // continue;
        }
//        Map<String, Long> modelMap = modelList.stream().collect(
//                Collectors.toMap(e -> e.getStoreId() + ":" + e.getThirdId(), ThirdVehicleIdRelationEntity::getSaasId));

        // 查询哈啰商品列表
        req.setMerchantId(helloMerchantId);
        req.setSiteIds(HelloStoreIdList);
        req.setPageIndex(1);
        req.setPageSize(30);
        boolean loopFlg = true;
        while (loopFlg) {
            ResultResp<HelloGoodsInfoList> goodsListResp = helloClientWrapper.goodsList(merchantId, req);
            logger.infov("请求hello接口报文 商品列表for价格拉取,merchantId: {0},request={1},response={2}", merchantId, req,
                    goodsListResp);
            if (goodsListResp.isSuccess()) {
                HelloGoodsInfoList data = goodsListResp.getData();
                if (data != null && data.getItems() != null && !data.getItems().isEmpty()) {
                    // 价格拉取导入
                    initHelloGoodsPrice(merchantId, storeMap, modelList, data);
                } else {
                    loopFlg = false;
                }
            } else {
                this.initFailV2(merchantId, goodsListResp.getMsg(), "拉取哈啰价格失败终止", 60);
                return SaasResultResp.failed(goodsListResp.getCode(), goodsListResp.getMsg());
            }
            req.setPageIndex(req.getPageIndex() + 1);
        }
        this.initSuccessV2(merchantId, 60);

        // 异步调用服务
        scheduledExecutor.schedule(() -> {
            try  {
                // 记录异步任务的日志
                logger.infov("[HELLO]开始执行增值服务初始化开始, merchantId:{0}", merchantId);
                PlatformSyncParam platformSyncParam = new PlatformSyncParam();
                platformSyncParam.setBusiType(null);
                platformSyncParam.setMerchantId(merchantId);
                this.insuranceAddedServiceUpdateNotify(platformSyncParam);
                logger.infov("[HELLO]开始执行增值服务初始化成功, merchantId:{0}", merchantId);
            } catch (Exception e) {
                logger.infov("[HELLO]开始执行增值服务初始化异常, merchantId:{0}, error={1}", merchantId, e.getMessage());;
            }
        }, 5, TimeUnit.SECONDS);

        return SaasResultResp.success();
    }

    /**
     * 价格处理
     *
     * @param merchantId
     * @param storeMap
     * @param modelList
     * @param dataList
     */
    private void initHelloGoodsPrice(Long merchantId, Map<String, Long> storeMap, List<ThirdVehicleIdRelationEntity> modelList,
            HelloGoodsInfoList dataList) {
        // 价格转换并保存到SaaS
        PriceDatePull priceDatePull = new PriceDatePull();
        priceDatePull.setChannelId(HELLO);
        priceDatePull.setMerchantId(merchantId);
        List<PriceDatePull.PriceDate> priceDateList = new ArrayList<>();
        for (HelloGoodsInfoDetail item : dataList.getItems()) {
            Long storeId = storeMap.get(String.valueOf(item.getSite().getSiteId()));
            if (storeId == null) {
                continue;
            }
            List<ThirdVehicleIdRelationEntity> tmpList = modelList.stream()
                    .filter(e -> e.getThirdId().equals(item.getGoodsId()) && e.getStoreId().equals(storeId))
                    .collect(Collectors.toList());
            for (ThirdVehicleIdRelationEntity model : tmpList) {
                PriceDatePull.PriceDate priceDate = new PriceDatePull.PriceDate();
                Long vehicleModelId = model.getSaasId();
                priceDate.setStoreId(storeId);
                priceDate.setVehicleModelId(vehicleModelId);
                // 里程限制
                priceDate.setMileageRent(0L);
                priceDate.setMileageLimit((byte) 0);
                priceDate.setMileage((short) 0);
                // 转换押金
                priceDate.setIllegalDeposit(0L);
                priceDate.setRentDeposit(0L);
                if (item.getFeeInfo() == null) {
                    item.setFeeInfo(new ArrayList<>());
                }
                for (HelloGoodsInfoDetail.FeeInfo deposit : item.getFeeInfo()) {
                    // 10:车辆押金 11:违章押金
                    if (deposit.getFeeType() == null) {
                        continue;
                    } else if (deposit.getFeeType() == 11) {
                        priceDate.setIllegalDeposit(yuanToFen(deposit.getFeeValue()));
                    } else if (deposit.getFeeType() == 10) {
                        priceDate.setRentDeposit(yuanToFen(deposit.getFeeValue()));
                    }
                }
                // 转换基础价格，平日周末价格
                if (item.getGoodsPrice() == null) {
                    item.setGoodsPrice(new ArrayList<>());
                }
                List<PriceDatePull.PriceVo> priceList = new ArrayList<>();
                for (HelloGoodsInfoDetail.GoodsPrice goodsPrice : item.getGoodsPrice()) {
                    if (goodsPrice.getPrice() != null) {
                        PriceDatePull.PriceVo vo = new PriceDatePull.PriceVo();
                        // dimensionType 1:平日 2:周日 3:时间段
                        if (goodsPrice.getDimensionType() == null) {
                            continue;
                        } else if (goodsPrice.getDimensionType() == 1) {
                            vo.setPriceType(PriceTypeEnum.BASE_PRICE_WEEKDAY.getValue());
                            vo.setPriceName(PriceTypeEnum.BASE_PRICE_WEEKDAY.getName());
                        } else if (goodsPrice.getDimensionType() == 2) {
                            vo.setPriceType(PriceTypeEnum.BASE_PRICE_WEEKEND.getValue());
                            vo.setPriceName(PriceTypeEnum.BASE_PRICE_WEEKEND.getName());
                            // 周一到周四是周中，周五到周末为周末
                            String pricePeriod = "[5,6,7]";
                            try {
                                pricePeriod = new ObjectMapper().writeValueAsString(goodsPrice.getWeekdayList());
                            } catch (Exception e) {

                            }
                            vo.setDayEnd(pricePeriod);
                        } else if (goodsPrice.getDimensionType() == 3) {
                            vo.setPriceType(PriceTypeEnum.DAYS_PRICE.getValue());
                            vo.setPriceName(PriceTypeEnum.DAYS_PRICE.getName());
                            HelloGoodsInfoDetail.TimePeriod timePeriod = goodsPrice.getTimePeriod();
                            if (null != timePeriod) {
                                vo.setDayStart(timePeriod.getStartDate());
                                vo.setDayEnd(timePeriod.getEndDate());
                            } else {
                                continue;
                            }
                        } else {
                            continue;
                        }
                        vo.setPrice(yuanToFen(goodsPrice.getPrice()));
                        priceList.add(vo);
                    }
                }
                priceDate.setPriceList(priceList);
                // 转换保险&附加服务价格
                if (item.getGoodsService() == null) {
                    item.setGoodsService(new ArrayList<>());
                }
                List<PriceDatePull.ServicVo> servicePriceList = new ArrayList<>();
                for (HelloGoodsInfoDetail.GoodsService goodsService : item.getGoodsService()) {
                    /**
                     * 1:基础保障
                     * 2:优享保障
                     * 3:尊享服务保障
                     * 4:儿童座椅0-7
                     * 5:儿童座椅0-12
                     */
                    PriceDatePull.ServicVo vo = new PriceDatePull.ServicVo();
                    // dimensionType 1:平日 2:周日 3:时间段
                    if (goodsService.getGoodsServiceType() == null) {
                        continue;
                    } else if (goodsService.getGoodsServiceType() == 1) {
                        vo.setPriceType(FeeTypeEnum.BASIC_SERVICE_FEE.getValue());
                        vo.setPriceName(FeeTypeEnum.BASIC_SERVICE_FEE.getName());
                    } else if (goodsService.getGoodsServiceType() == 2) {
                        vo.setPriceType(FeeTypeEnum.ADVANCED_SERVICE_FEE.getValue());
                        vo.setPriceName(FeeTypeEnum.ADVANCED_SERVICE_FEE.getName());
                    } else if (goodsService.getGoodsServiceType() == 3) {
                        vo.setPriceType(FeeTypeEnum.PREMIUM_SERVICE_FEE.getValue());
                        vo.setPriceName(FeeTypeEnum.PREMIUM_SERVICE_FEE.getName());
                    } else if (goodsService.getGoodsServiceType() == 5) {
                        vo.setPriceType(FeeTypeEnum.CHILD_SEAT.getValue());
                        vo.setPriceName(FeeTypeEnum.CHILD_SEAT.getName());
                    } else {
                        continue;
                    }
                    if (goodsService.getServiceGoodsPrice() != null) {
                        vo.setPrice(yuanToFen(goodsService.getServiceGoodsPrice().getServicePrice()));
                    }
                    servicePriceList.add(vo);
                }
                priceDate.setServicePriceList(servicePriceList);
                priceDateList = new ArrayList<>();
                priceDateList.add(priceDate);
                priceDatePull.setPriceDateList(priceDateList);
                ApiResultResp<Boolean> result = saasPriceClient.doInitPriceDate(priceDatePull);
                logger.infov("hello价格初始化导入,merchantId:{0}, result={1}, request={2}", merchantId, result, priceDatePull);
            }
        }
//        priceDatePull.setPriceDateList(priceDateList);
//
//        if (!priceDateList.isEmpty()) {
//            ApiResultResp<Boolean> result = saasPriceClient.doInitPriceDate(priceDatePull);
//        } else {
//
//        }
    }

    private Long yuanToFen(Double price) {
        if (price == null) {
            return 0L;
        }
        return (long) (price * 100);
    }

    @Override
    @WithSpan("[hello]价格更新推送消费")
    @Transactional
    public void goodsPriceNotify(PlatformSyncParam platformSyncParam) {
        Span span = Span.current();
        Long merchantId = platformSyncParam.getMerchantId();
        PriceInfoReq priceInfoReq = this.deserialize(platformSyncParam.getData(), PriceInfoReq.class);
        if (priceInfoReq == null) {
            throw new BizException(ClientName.HELLO, ErrorCode.MODEL_VALID.getCode(), ErrorCode.MODEL_VALID.getDesc());
        }
        Long storeId = priceInfoReq.getStoreId();
        Long vehicleModelId = priceInfoReq.getVehicleModelId();
        this.doPriceModifyNotify(merchantId, vehicleModelId, storeId, span);
    }

    @Override
    @WithSpan("[hello]价格启停售推送消费")
    @Transactional
    public void goodsPriceStatusNotify(PlatformSyncParam platformSyncParam) {
        logger.infov("价格启停售SAAS触发,参数platformSyncParam:{0}", platformSyncParam);
        Span span = Span.current();
        Long merchantId = platformSyncParam.getMerchantId();
        PriceInfoReq priceInfoReq = this.deserialize(platformSyncParam.getData(), PriceInfoReq.class);
        if (priceInfoReq == null) {
            throw new BizException(ClientName.HELLO, ErrorCode.MODEL_VALID.getCode(), ErrorCode.MODEL_VALID.getDesc());
        }
        Long storeId = priceInfoReq.getStoreId();
        Long vehicleModelId = priceInfoReq.getVehicleModelId();
        helloVehicleService.batchVehicleOfflineNotify(Arrays.asList(vehicleModelId), Arrays.asList(storeId),
                merchantId);
    }

    @Override
    public Boolean syncPrice(Long vehicleModelId, Long storeId, Long merchantId) {
        return this.doPriceModifyNotify(merchantId, vehicleModelId, storeId, Span.current());
    }

    private boolean doPriceModifyNotify(Long merchantId, Long vehicleModelId, Long storeId, Span span) {
        // 日志查询用key
        String logKey = String.format("logKey:%s_%s_%s,%s_%s", merchantId, storeId, vehicleModelId, storeId,
                vehicleModelId);
        // 检查渠道是否已开通
        String helloMerchantId = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));
        if (helloMerchantId == null) {
            logger.errorv("哈啰价格推送,未找到第三方商家ID,saasMerchantId={0},{1}", helloMerchantId, logKey);
            return false;
        }
        span.setAttribute("helloMerchantId", helloMerchantId);
        span.setAttribute("merchantId", merchantId);
        span.setAttribute("logKey", logKey);

        HelloGoodsPriceSaveReq helleReq = new HelloGoodsPriceSaveReq();

        // 获取第三方门店ID
        String thirdStoreId = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Store.STORE.getType(), storeId)
                .map(ThirdIdRelationEntity::getThirdId)
                .orElse(null);
        if (thirdStoreId == null) {
            logger.errorv("[HELLO]哈啰价格推送,未找到第三方门店ID,saasStoreId={0},{1}", storeId, logKey);
            return false;
        }
        Long helloStoreId = Long.parseLong(thirdStoreId);

        // 获取第三方商品ID
        String thirdGoodsId = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(HELLO, merchantId, storeId,
                        IdRelationEnum.Vehicle.VEHICLE_MODEL.getType(), vehicleModelId)
                .map(ThirdVehicleIdRelationEntity::getThirdId)
                .orElse(null);
        if (thirdGoodsId == null) {
            logger.errorv("[HELLO]哈啰价格推送,未找到第三方商品ID,saasStoreId={0},saasModelId={1},{2}", storeId, vehicleModelId, logKey);
            return false;
        }
        Long helloGoodsId = Long.parseLong(thirdGoodsId);

        // hello商家ID
        helleReq.setMerchantId(helloMerchantId);
        // 临时设置商品信息 @孙 @郑
        setGoodsBasic(helleReq, helloStoreId, helloGoodsId);

        // 获取保险&服务第三方ID
//        Map<Byte, String> serviceMap = thirdVehicleIdRelationRepository
//                .findByChannelIdAndMerchantIdAndStoreIdAndTypes(HELLO, merchantId, storeId,
//                        Arrays.asList(IdRelationEnum.Vehicle.BASIC_INSURANCE.getType(),
//                                IdRelationEnum.Vehicle.PREMIUM_INSURANCE.getType(),
//                                IdRelationEnum.Vehicle.LUXURY_INSURANCE.getType()))
//                .stream().collect(Collectors.toMap(e -> e.getType(), e -> e.getThirdId()));
        Map<Byte, String> serviceMap = new HashMap<>();
        if (!"prod".equals(env)) {
            // 测试环境
            serviceMap.put(IdRelationEnum.Vehicle.BASIC_INSURANCE.getType(), "7307484793941721093");
            serviceMap.put(IdRelationEnum.Vehicle.PREMIUM_INSURANCE.getType(), "7307484793941721097");
            serviceMap.put(IdRelationEnum.Vehicle.LUXURY_INSURANCE.getType(), "7307484793941721101");
        } else {
            // 生产环境
            serviceMap.put(IdRelationEnum.Vehicle.BASIC_INSURANCE.getType(), "7310506049942989281");
            serviceMap.put(IdRelationEnum.Vehicle.PREMIUM_INSURANCE.getType(), "7310506049942989285");
            serviceMap.put(IdRelationEnum.Vehicle.LUXURY_INSURANCE.getType(), "7310506049942989289");
        }
        // 儿童座椅
        List<ThirdVehicleIdRelationEntity> setList = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndType(HELLO, merchantId, IdRelationEnum.Vehicle.ADDED_SERVICE.getType());
        if (!setList.isEmpty()) {
            serviceMap.put(setList.get(0).getType(), setList.get(0).getThirdId());
        }
        // 设置增值服务IDS
        helleReq.setGoodsServiceIds(serviceMap.values().stream().map(Long::parseLong).collect(Collectors.toList()));
        // 价格转换
        transPrice(merchantId, storeId, vehicleModelId, helleReq, helloStoreId, helloGoodsId, serviceMap);

        // 调用hello保存接口
        String helloRequestJson = null;
        try {
            helloRequestJson = new ObjectMapper().writeValueAsString(helleReq);
        } catch (Exception e) {
            logger.errorv(
                    "[HELLO]哈啰价格推送,失败,请求参数格式化异常;merchantId:{0},storeId:{1},vehicleModelId:{2},resultMsg:{3},request:{4},{5}",
                    merchantId, storeId, vehicleModelId, e.getMessage(), helleReq, logKey);
        }
        ResultResp<StoreModifyNotifyResp> helloResp = helloClientWrapper.priceModifyNotify(merchantId, helleReq);
        if (!helloResp.isSuccess()) {
            logger.errorv("[HELLO]哈啰价格推送,失败;merchantId:{0},storeId:{1},vehicleModelId:{2},resultMsg:{3},request:{4},{5}",
                    merchantId, storeId, vehicleModelId, helloResp.getMsg(), helloRequestJson, logKey);
            span.setStatus(StatusCode.ERROR);
            span.setAttribute("errorCode", helloResp.getCode());
            span.setAttribute("errorMsg", helloResp.getMsg());
            return false;
        }

        logger.infov("[HELLO]哈啰价格推送,成功;merchantId:{0},storeId:{1},vehicleModelId:{2},resultMsg:{3},request:{4},{5}",
                merchantId, storeId, vehicleModelId, helloResp.getMsg(), helloRequestJson, logKey);
        span.setStatus(StatusCode.OK);
        span.addEvent("哈啰价格推送成功", Attributes.builder()
                .put("merchantId", merchantId)
                .put("storeId", storeId)
                .put("vehicleModelId", vehicleModelId)
                .build());
        return true;
    }

    private void setGoodsBasic(HelloGoodsPriceSaveReq helleReq, Long helloStoreId, Long helloGoodsId) {
        HelloGoodsPriceSaveReq.GoodsBasic goodsBasic = new HelloGoodsPriceSaveReq.GoodsBasic();
        goodsBasic.setId(String.valueOf(helloGoodsId));
        // goodsBasic.setMerchantId("20000038603");
        // goodsBasic.setSiteId("7301553626531273015");
        // goodsBasic.setProductName("测试商品");
        // goodsBasic.setVehicleModelId("1868617150461558786");
        // goodsBasic.setVehicleSeriesId("1514260158193803265");
        // goodsBasic.setVehicleBrandId("1514260148419530754");

        // goodsBasic.setLicenseTag(1);
        // goodsBasic.setStatus(1);
        // goodsBasic.setAssureRent(true);
        // goodsBasic.setAssureApply(true);
        // goodsBasic.setCreatedBy("admin");
        // goodsBasic.setCurVersion(1);
        // goodsBasic.setCreateTime("2023-03-01 00:00:00");
        // goodsBasic.setUpdatedBy("admin");
        // goodsBasic.setUpdateTime("2023-03-01 00:00:00");
        helleReq.setGoodsBasic(goodsBasic);
    }

    private void transPrice(Long merchantId, Long storeId, Long vehicleModelId, HelloGoodsPriceSaveReq helleReq,
            Long helloStoreId, Long helloGoodsId, Map<Byte, String> serviceMap) {
        BasePriceParam basePriceParam = new BasePriceParam();
        basePriceParam.setMerchantId(merchantId);
        basePriceParam.setStoreId(storeId);
        basePriceParam.setVehicleModelId(vehicleModelId);
        basePriceParam.setChannel(HELLO);
        ApiResultResp<OpenProductPriceCalendarResp> saasResp = saasPriceClient.getStoreVehiclePrice(basePriceParam);
        if (!ApiResultResp.isSuccess(saasResp)) {
            throw new BizException(ClientName.HELLO, saasResp.getCode(), saasResp.getMsg());
        }

        // 把渠道价格转换成hello价格
        if (saasResp.getData() != null && saasResp.getData().getSkuPriceCalendarList() != null) {
            for (OpenProductPriceCalendarResp.ProductPriceCalendar productPriceCalendar : saasResp.getData()
                    .getSkuPriceCalendarList()) {
                // 保险费用
                List<HelloGoodsPriceSaveReq.ServiceGoods> serviceGoods = new ArrayList<>();
                for (OpenProductPriceCalendarResp.FeePriceRequest feePrice : productPriceCalendar
                        .getInsurancePriceList()) {
                    HelloGoodsPriceSaveReq.ServiceGoods serviceGood = convertInsurancePrice(helloGoodsId, feePrice,
                            serviceMap);
                    if (serviceGood != null) {
                        serviceGoods.add(serviceGood);
                    } else {
                        continue;
                    }
                }
                // 附加服务
                for (OpenProductPriceCalendarResp.FeePriceRequest feePrice : productPriceCalendar
                        .getAddProductPriceList()) {
                    HelloGoodsPriceSaveReq.ServiceGoods serviceGood = convertInsurancePrice(helloGoodsId, feePrice,
                            serviceMap);
                    if (serviceGood != null) {
                        serviceGoods.add(serviceGood);
                    } else {
                        continue;
                    }
                }
                helleReq.setServiceGoods(serviceGoods);
                // 押金处理
                helleReq.setGoodsFee(convertDeposits(helloGoodsId, productPriceCalendar));
                // 租金处理
                helleReq.setPrice(convertRentPrice(helloGoodsId, productPriceCalendar));
            }
        }
        return;
    }

    private HelloGoodsPriceSaveReq.ServiceGoods convertInsurancePrice(Long helloGoodsId,
            OpenProductPriceCalendarResp.FeePriceRequest feePrice, Map<Byte, String> serviceMap) {
        /**
         * 1:基础保障
         * 2:优享保障
         * 3:尊享服务保障
         * 4:儿童座椅0-7
         * 5:儿童座椅0-12
         */
        HelloGoodsPriceSaveReq.ServiceGoods serviceGoods = new HelloGoodsPriceSaveReq.ServiceGoods();
        int serviceType = 0;
        Long serviceId = null;
        String serviceCode;
        switch (feePrice.getFeeCode()) {
            case "1002":
                serviceType = 1;
                serviceCode = serviceMap.get(IdRelationEnum.Vehicle.BASIC_INSURANCE.getType());
                break;
            case "2001":
                serviceType = 2;
                serviceCode = serviceMap.get(IdRelationEnum.Vehicle.PREMIUM_INSURANCE.getType());
                break;
            case "2011":
                serviceType = 3;
                serviceCode = serviceMap.get(IdRelationEnum.Vehicle.LUXURY_INSURANCE.getType());
                break;
            case "2003":
                serviceType = 5;
                serviceCode = serviceMap.get(IdRelationEnum.Vehicle.ADDED_SERVICE.getType());
                break;
            default:
                return null;
        }
        if (serviceCode == null) {
            return null;
        }
        // 增值服务id
        serviceGoods.setGoodId(helloGoodsId);
        // goodsServiceId
        serviceGoods.setGoodsServiceId(Long.valueOf(serviceCode));
        // 增值服务名称
        serviceGoods.setGoodsServiceName(feePrice.getFeeName());
        // 增值服务类型
        serviceGoods.setGoodsServiceType(serviceType);
        // ID
        serviceGoods.setId(null);
        // 服务价格
        serviceGoods.setServicePrice((long) feePrice.getPrice() / 100);
        return serviceGoods;
    }

    private List<HelloGoodsPriceSaveReq.GoodsFee> convertDeposits(Long helloGoodsId,
            OpenProductPriceCalendarResp.ProductPriceCalendar productPriceCalendar) {
        List<HelloGoodsPriceSaveReq.GoodsFee> goodsFees = new ArrayList<>();
        HelloGoodsPriceSaveReq.GoodsFee goodsFee = convertDeposit(helloGoodsId,
                productPriceCalendar.getRentalDeposit());
        if (goodsFee != null) {
            goodsFees.add(goodsFee);
        }
        goodsFee = convertDeposit(helloGoodsId, productPriceCalendar.getIllegalDeposit());
        if (goodsFee != null) {
            goodsFees.add(goodsFee);
        }
        return goodsFees;
    }

    private HelloGoodsPriceSaveReq.GoodsFee convertDeposit(Long helloGoodsId,
            OpenProductPriceCalendarResp.FeePriceRequest feePriceRequest) {
        /**
         * 10:车辆押金
         * 11:违章押金
         */
        int feeType = 0;
        switch (feePriceRequest.getFeeCode()) {
            case "4003":
                feeType = 10;
                break;
            case "4004":
                feeType = 11;
                break;
            default:
                return null;
        }
        HelloGoodsPriceSaveReq.GoodsFee goodsFee = new HelloGoodsPriceSaveReq.GoodsFee();
        goodsFee.setFeeId(null);
        goodsFee.setFeeName(feePriceRequest.getFeeName());
        goodsFee.setExplain(feePriceRequest.getFeeName());
        goodsFee.setFeeType(feeType);
        goodsFee.setFeeValue((long) feePriceRequest.getPrice() / 100);
        return goodsFee;
    }

    private List<HelloGoodsPriceSaveReq.Price> convertRentPrice(Long helloGoodsId,
            OpenProductPriceCalendarResp.ProductPriceCalendar productPriceCalendar) {
        /**
         * dimensionType
         * 1:平日
         * 2:周日
         * 3:时间段
         */
        List<HelloGoodsPriceSaveReq.Price> transPrice = new ArrayList<>();
        // 平日周日处理
        List<OpenProductPriceCalendarResp.DailyPriceWeekdayRequest> prices = productPriceCalendar
                .getDailyPriceWeekdayList().stream().filter(e -> e.getWeekend().equals("0"))
                .collect(Collectors.toList());
        List<OpenProductPriceCalendarResp.DailyPriceWeekdayRequest> weekendPrices = productPriceCalendar
                .getDailyPriceWeekdayList().stream().filter(e -> e.getWeekend().equals("1"))
                .collect(Collectors.toList());
        transPrice.add(convertWeekPrice(helloGoodsId, prices));
        transPrice.add(convertWeekPrice(helloGoodsId, weekendPrices));
        // 日租金-节假日数据
        List<OpenProductPriceCalendarResp.DailyPriceHolidayRequest> holidayPrices = productPriceCalendar
                .getDailyPriceHolidayList();
        for (OpenProductPriceCalendarResp.DailyPriceHolidayRequest holidayPrice : holidayPrices) {
            HelloGoodsPriceSaveReq.Price price = new HelloGoodsPriceSaveReq.Price();
            price.setId(null);
            price.setGoodsId(helloGoodsId);
            price.setDimensionType(3);
            price.setWeekdayList(null);
            price.setPrice((long) holidayPrice.getPrice() / 100);
            HelloGoodsPriceSaveReq.TimePeriod timePeriod = new HelloGoodsPriceSaveReq.TimePeriod();
            timePeriod.setStartDate(holidayPrice.getDayStart());
            timePeriod.setEndDate(holidayPrice.getDayEnd());
            price.setTimePeriod(timePeriod);
            transPrice.add(price);
        }
        // 日租金-特殊时间段数据
        List<OpenProductPriceCalendarResp.DailyPriceSpecialRequest> specialPrices = productPriceCalendar
                .getDailyPriceSpecialList();
        for (OpenProductPriceCalendarResp.DailyPriceSpecialRequest specialPrice : specialPrices) {
            HelloGoodsPriceSaveReq.Price price = new HelloGoodsPriceSaveReq.Price();
            price.setId(null);
            price.setGoodsId(helloGoodsId);
            price.setDimensionType(3);
            price.setWeekdayList(null);
            price.setPrice((long) specialPrice.getPrice() / 100);
            HelloGoodsPriceSaveReq.TimePeriod timePeriod = new HelloGoodsPriceSaveReq.TimePeriod();
            timePeriod.setStartDate(specialPrice.getDayStart());
            timePeriod.setEndDate(specialPrice.getDayEnd());
            price.setTimePeriod(timePeriod);
            transPrice.add(price);
        }
        // 日租金-单日价格
        List<OpenProductPriceCalendarResp.RentDayPrice> rentDayPrices = productPriceCalendar.getRentDayPriceList();
        for (OpenProductPriceCalendarResp.RentDayPrice rentDayPrice : rentDayPrices) {
            HelloGoodsPriceSaveReq.Price price = new HelloGoodsPriceSaveReq.Price();
            price.setId(null);
            price.setGoodsId(helloGoodsId);
            price.setDimensionType(3);
            price.setWeekdayList(null);
            price.setPrice((long) rentDayPrice.getPrice() / 100);
            HelloGoodsPriceSaveReq.TimePeriod timePeriod = new HelloGoodsPriceSaveReq.TimePeriod();
            timePeriod.setStartDate(rentDayPrice.getRentDate());
            timePeriod.setEndDate(rentDayPrice.getRentDate());
            price.setTimePeriod(timePeriod);
            transPrice.add(price);
        }

        // 删除价格值为0的数据
        transPrice = transPrice.stream().filter(e -> e.getPrice() != null && e.getPrice() > 0).toList() ;

        return transPrice;
    }

    private HelloGoodsPriceSaveReq.Price convertWeekPrice(Long helloGoodsId,
            List<OpenProductPriceCalendarResp.DailyPriceWeekdayRequest> weekPrices) {
        int dimensionType = weekPrices.get(0).getWeekend().equals("0") ? 1 : 2;
        List<Integer> weekdayList = new ArrayList<>();
        for (OpenProductPriceCalendarResp.DailyPriceWeekdayRequest weekPrice : weekPrices) {
            if (weekPrice.getDayStart().equals(weekPrice.getDayEnd())) {
                weekdayList.add(Integer.parseInt(weekPrice.getDayStart()));
            } else {
                for (int i = Integer.parseInt(weekPrice.getDayStart()); i <= Integer
                        .parseInt(weekPrice.getDayEnd()); i++) {
                    weekdayList.add(i);
                }
            }
        }
        HelloGoodsPriceSaveReq.Price price = new HelloGoodsPriceSaveReq.Price();
        price.setId(null);
        price.setGoodsId(helloGoodsId);
        price.setDimensionType(dimensionType);
        price.setWeekdayList(weekdayList);
        price.setPrice((long) weekPrices.get(0).getPrice() / 100);
        return price;
    }


    @WithSpan("[hello]保险设置更新消费")
    public void insuranceUpdateNotify(Long merchantId, String thirdMerchantId, Long storeId, InsuranceServiceSettingDTO insuranceServiceSettingDTO) {
        Span span = Span.current();
        if (insuranceServiceSettingDTO == null || insuranceServiceSettingDTO.getId() == null
                || insuranceServiceSettingDTO.getName() == null) {
            span.addEvent("解析保险服务设置DTO失败", Attributes.builder()
                    .put("data", String.valueOf(insuranceServiceSettingDTO))
                    .build());
            return;
        }

        // 门店保险零散小时
        SaasResponse<List<StoreHourlyChargeDTO>> storeHourlyChargeResponse = saasStoreClient.storeCharge(HELLO,
                merchantId);
        if (storeHourlyChargeResponse.getData() == null || !storeHourlyChargeResponse.isOk()) {
            span.addEvent("未找到对应的门店零散小时信息", Attributes.builder()
                    .put("merchantId", merchantId)
                    .build());
            return;
        }
        List<Long> storeIds = new ArrayList<>();
        if (storeId == null) {
            storeIds = storeHourlyChargeResponse.getData().stream()
                    .map(StoreHourlyChargeDTO::getStoreId)
                    .collect(Collectors.collectingAndThen(Collectors.toSet(), ArrayList::new));
        }
        else {
            storeIds.add(storeId);
        }
        Byte chargeItem = INSURANCE_ADDED_TYPE_MAP.get(insuranceServiceSettingDTO.getName());
        for (Long tempStoreId : storeIds) {
            List<StoreHourlyChargeDTO> hourlyChargeVo = storeHourlyChargeResponse.getData().stream()
                    .filter(storeHourlyChargeDTO -> Objects.equals(storeHourlyChargeDTO.getChargeItem(), chargeItem) &&
                            Objects.equals(storeHourlyChargeDTO.getStoreId(), tempStoreId))
                    .toList();
            // 查询是否已存在helloId-saasId映射关系
            String helloId = getHelloInsuranceMappingFromSaas(merchantId, tempStoreId, Long.valueOf(chargeItem),
                    insuranceServiceSettingDTO.getName());
            if (helloId != null) {
                // 构建hello保险数据请求
                HelloInsuranceUpdateConverter helloInsuranceUpdateConverter = new HelloInsuranceUpdateConverter(
                        thirdMerchantId, hourlyChargeVo, helloId);
                InsuranceModifyNotifyReq insuranceModifyNotifyReq = helloInsuranceUpdateConverter
                        .convert(insuranceServiceSettingDTO);

                // 调用保险变更接口
                ResultResp<InsuranceModifyNotifyResp> resultResp = helloClientWrapper.insuranceModifyNotify(merchantId, insuranceModifyNotifyReq);
                if (!resultResp.isSuccess()) {
                    Attributes attributes = Attributes.builder()
                            .put("errorMsg", resultResp.getMsg())
                            .build();
                    span.addEvent("调用hello更新保险接口 失败", attributes);
                }
            } else {
                // 构建hello保险数据请求
                HelloInsuranceCreateConverter helloInsuranceCreateConverter = new HelloInsuranceCreateConverter(
                        thirdMerchantId, hourlyChargeVo);
                InsuranceCreateReq insuranceCreateReq = helloInsuranceCreateConverter
                        .convert(insuranceServiceSettingDTO);

                // 调用保险新增接口
                ResultResp<InsuranceCreateNotifyResp> resultResp = helloClientWrapper.insuranceCreateNotify(merchantId,
                        insuranceCreateReq);
                if (!resultResp.isSuccess() || resultResp.getData().getId() == null) {
                    Attributes attributes = Attributes.builder()
                            .put("errorMsg", resultResp.getMsg())
                            .build();
                    span.addEvent("调用hello新增保险接口 失败", attributes);
                    continue;
                }
                // 更改表数据
                InsuranceCreateNotifyResp respData = resultResp.getData();
                saveOrUpdateInsuranceMapping(merchantId, tempStoreId, chargeItem, insuranceServiceSettingDTO.getName(),
                        respData.getId());
                span.addEvent("更新保险映射关系成功", Attributes.builder()
                        .put("merchantId", merchantId)
                        .put("newId", respData.getId())
                        .build());
            }
        }
        span.setStatus(StatusCode.OK);
        span.addEvent("调用hello更新保险接口 成功", Attributes.builder().build());
    }


    @WithSpan("[hello]附加服务更新推送消费")
    public void addedServiceUpdateNotify(Long merchantId, String thirdMerchantId, Long storeId) {
        Span span =Span.current();
        // 反查数据
        List<AccessoryProductsInventoryDTO> accessoryProductsInventoryVos = saasClient
                .getPurchasedAddProduct(merchantId).getData();
        if (accessoryProductsInventoryVos == null) {
            span.addEvent("未找到对应的儿童座椅库存信息", Attributes.builder()
                    .put("merchantId", merchantId)
                    .build());
            return;
        }
        SaasResponse<List<AddedServiceSettingDTO>> addedServiceResponse = saasClient.addedServiceList(merchantId);
        if (addedServiceResponse.getData().isEmpty() || addedServiceResponse.getData() == null) {
            span.addEvent("未找到对应的附加服务基础信息", Attributes.builder()
                    .put("merchantId", merchantId)
                    .build());
            return;
        }
        List<AddedServiceSettingDTO> addedServiceDTOs = addedServiceResponse.getData().stream()
                .filter(service -> service.getPreset() == 1 && "儿童座椅".equals(service.getName()))
                .toList();
        if (addedServiceDTOs.size() != 1 || addedServiceDTOs.get(0).getAttachmentProductInfoVoList() == null) {
            span.addEvent("附加服务基础信息错误", Attributes.builder()
                    .put("merchantId", merchantId)
                    .build());
            return;
        }
        List<Long> storeIds = new ArrayList<>();
        if (storeId == null) {
            storeIds = accessoryProductsInventoryVos.stream()
                .map(AccessoryProductsInventoryDTO::getStoreId)
                .collect(Collectors.toList());
        }
        else {
            storeIds.add(storeId);
        }
        List<ThirdIdRelationEntity> thirdIdRelationEntities = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndInSaasId(HELLO, merchantId,
                        IdRelationEnum.Store.STORE.getType(), storeIds)
                .stream()
                .collect(Collectors.toMap(
                        ThirdIdRelationEntity::getSaasId, entity -> entity, (existing, replacement) -> replacement))
                .values().stream().toList();
        Map<Long, String> storeIdToThirdIdMap = thirdIdRelationEntities.stream()
                .collect(Collectors.toMap(ThirdIdRelationEntity::getSaasId, ThirdIdRelationEntity::getThirdId));

        String helloId = getHelloAddedServiceMappingFromSaas(merchantId, 0L, 4L, "儿童座椅");
        if (helloId != null) {
            // 构建hello附加服务修改数据请求
            HelloAddedServiceUpdateConverter helloAddedServiceUpdateConverter = new HelloAddedServiceUpdateConverter(thirdMerchantId, storeIdToThirdIdMap, accessoryProductsInventoryVos, helloId);
            AddedServiceModifyNotifyReq addedServiceModifyNotifyReq = helloAddedServiceUpdateConverter.convert(addedServiceDTOs);
            if (addedServiceModifyNotifyReq == null) {
                Attributes attributes = Attributes.builder()
                        .put("merchantId", merchantId)
                        .build();
                span.addEvent("转换hello附加服务数据 失败", attributes);
                return;
            }
            // 调用附加服务变更接口
            ResultResp<AddedServiceModifyNotifyResp> resultResp = helloClientWrapper.addedServiceModifyNotify(merchantId, addedServiceModifyNotifyReq);
            if (!resultResp.isSuccess()) {
                Attributes attributes = Attributes.builder()
                        .put("errorMsg", resultResp.getMsg())
                        .build();
                span.addEvent("调用hello新增附加服务接口 失败", attributes);
            }
        }
        else {
            // 构建hello附加服务新增数据请求
            HelloAddedServiceCreateConverter helloAddedServiceCreateConverter = new HelloAddedServiceCreateConverter(thirdMerchantId, storeIdToThirdIdMap, accessoryProductsInventoryVos);
            AddedServiceCreateReq addedServiceCreateReq = helloAddedServiceCreateConverter.convert(addedServiceDTOs);
            if (addedServiceCreateReq == null) {
                Attributes attributes = Attributes.builder()
                        .put("merchantId", merchantId)
                        .build();
                span.addEvent("转换hello附加服务数据 失败", attributes);
                return;
            }
            // 调用附加服务新增接口
            ResultResp<AddedServiceCreateNotifyResp> resultResp = helloClientWrapper.addedServiceCreateNotify(merchantId, addedServiceCreateReq);
            if (!resultResp.isSuccess() || resultResp.getData().getId() == null) {
                Attributes attributes = Attributes.builder()
                        .put("errorMsg", resultResp.getMsg())
                        .build();
                span.addEvent("调用hello新增附加服务接口 失败", attributes);
                return;
            }
            // 更改表数据
            AddedServiceCreateNotifyResp respData = resultResp.getData();
            Byte chargeItem = INSURANCE_ADDED_TYPE_MAP.get("附加服务");
            saveOrUpdateAddedServiceMapping(merchantId, 0L, chargeItem, respData.getId());
            span.addEvent("更新附加服务映射关系成功", Attributes.builder()
                    .put("merchantId", merchantId)
                    .put("newId", respData.getId())
                    .build());
            span.setStatus(StatusCode.OK);
            span.addEvent("调用hello新增附加服务接口 成功", Attributes.builder().build());
        }
    }

    @Override
    @WithSpan("[hello]保险附加服务更新推送消费")
    @Transactional
    public void insuranceAddedServiceUpdateNotify(PlatformSyncParam platformSyncParam) {
        Span span = Span.current();
        Long merchantId = platformSyncParam.getMerchantId();
        Long storeId = null;
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));
        span.setAttribute("thirdMerchantId", thirdMerchantId);
        span.setAttribute("merchantId", merchantId);

        if (Objects.equals(platformSyncParam.getBusiType(), ModificationBusiTypeEnum.ADDED_SERVICE.getBusiType())) {
            addedServiceUpdateNotify(merchantId, thirdMerchantId, storeId);
        }
        else if (Objects.equals(platformSyncParam.getBusiType(), ModificationBusiTypeEnum.INSURANCE_UPDATE.getBusiType())) {
            InsuranceServiceSettingDTO insuranceServiceSettingDTO = this.deserialize(platformSyncParam.getData(), InsuranceServiceSettingDTO.class);
            insuranceUpdateNotify(merchantId, thirdMerchantId, storeId, insuranceServiceSettingDTO);
        }
        else if (Objects.equals(platformSyncParam.getBusiType(), ModificationBusiTypeEnum.STORE_SAVE.getBusiType()) || platformSyncParam.getBusiType() == null) {
            if (Objects.equals(platformSyncParam.getBusiType(), ModificationBusiTypeEnum.STORE_SAVE.getBusiType())) {
                storeId = this.deserialize(platformSyncParam.getData(), StoreInfoReq.class).getStoreId();
            }
            SaasResponse<List<InsuranceServiceSettingDTO>> insuranceServiceSettingResponse = saasClient.insuranceServiceList(merchantId);
            if (insuranceServiceSettingResponse.getData().isEmpty()) {
                Span.current().addEvent("未找到对应的保险基础信息", Attributes.builder()
                        .put("merchantId", merchantId)
                        .build());
                return;
            }
            List<InsuranceServiceSettingDTO> insuranceServiceSettingDTOs = insuranceServiceSettingResponse.getData().stream()
                    .filter(service -> service.getPreset() == 1)
                    .toList();
            addedServiceUpdateNotify(merchantId, thirdMerchantId, storeId);
            for (InsuranceServiceSettingDTO insuranceServiceSettingDTO : insuranceServiceSettingDTOs) {
                insuranceUpdateNotify(merchantId, thirdMerchantId, storeId, insuranceServiceSettingDTO);
            }
        }
        else {
            Span.current().addEvent("不支持的业务类型", Attributes.builder()
                    .put("busiType", platformSyncParam.getBusiType())
                    .build());
        }
        span.addEvent("推送保险附加服务成功", Attributes.builder()
                .put("merchantId", merchantId)
                .build());
        span.setStatus(StatusCode.OK);
    }

    @Override
    public SaasResultResp<OrderListResp> orderList(SaasOrderListReq data) {
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelId(data.getMerchantId(), HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));
        OrderListReq req = new OrderListReq();
        req.setMerchantId(thirdMerchantId);
        req.setPageIndex(data.getPageIndex());
        req.setPageSize(data.getPageSize());
        req.setOrderStatusList(data.getOrderStatusList());
        req.setExpectedPickupTimeEnd(data.getExpectedPickUpTimeEnd());
        req.setExpectedPickupTimeStart(data.getExpectedPickUpTimeStart());
        req.setExpectedReturnTimeEnd(data.getExpectedReturnTimeEnd());
        req.setExpectedReturnTimeStart(data.getExpectedReturnTimeStart());
        ResultResp<OrderListResp> resultResp = helloClientWrapper.orderList(data.getMerchantId(), req);
        return SaasResultResp.success(resultResp.getData());
    }

    @Override
    public SaasResultResp<OrderDetailResp> orderDetail(SaasOrderDetailReq data) {
        String thirdMerchantId = apiConnRepository.findByMerchantIdAndChannelId(data.getMerchantId(), HELLO)
                .map(ApiConnEntity::getChannelVendorCode)
                .orElseThrow(() -> new BizException(ClientName.HELLO, ErrorCode.NOT_FOUND_MERCHANT.getCode(),
                        ErrorCode.NOT_FOUND_MERCHANT.getDesc()));
        OrderDetailReq req = new OrderDetailReq();
        req.setMerchantId(thirdMerchantId);
        req.setOutOrderNo(data.getThirdOrderId());
        ResultResp<OrderDetailResp> resultResp = helloClientWrapper.orderDetail(data.getMerchantId(), req);
        return SaasResultResp.success(resultResp.getData());
    }

    /**
     * 保存或更新库存ID映射
     */
    private void saveOrUpdateStockMapping(Long merchantId, Long storeId, Long saasStockId, String thirdStockId) {
        ThirdVehicleIdRelationEntity entity = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.STOCK.getType(), saasStockId)
                .orElse(new ThirdVehicleIdRelationEntity());

        entity.setMerchantId(merchantId);
        entity.setStoreId(storeId);
        entity.setChannelId(HELLO);
        entity.setType(IdRelationEnum.Vehicle.STOCK.getType());
        entity.setThirdId(thirdStockId);
        entity.setSaasId(saasStockId);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setOpTime(System.currentTimeMillis());
        thirdVehicleIdRelationRepository.save(entity);
    }

    /**
     * 获取库存映射ID
     */
    private String getHelloStockMappingFromSaas(Long merchantId, Long saasStockId) {
        return thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.STOCK.getType(), saasStockId)
                .map(ThirdVehicleIdRelationEntity::getThirdId)
                .orElse(null);
    }

    private static final Map<String, Byte> INSURANCE_ADDED_TYPE_MAP = Map.of(
            "基础保险", (byte) 1,
            "优享保险", (byte) 2,
            "尊享保险", (byte) 3,
            "附加服务", (byte) 4);

    /**
     * 保存或更新保险ID映射
     */
    private void saveOrUpdateInsuranceMapping(Long merchantId, Long storeId, Byte chargeItem, String insuranceName,
            String thirdId) {
        byte idRelationType = IdRelationEnum.Vehicle.getTypeByDesc(insuranceName);
        ThirdVehicleIdRelationEntity entity = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(HELLO, merchantId, storeId, idRelationType,
                        (long) chargeItem)
                .orElse(new ThirdVehicleIdRelationEntity());

        entity.setMerchantId(merchantId);
        entity.setStoreId(storeId);
        entity.setChannelId(HELLO);
        entity.setType(idRelationType);
        entity.setThirdId(thirdId);
        entity.setSaasId((long) chargeItem);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setOpTime(System.currentTimeMillis());

        thirdVehicleIdRelationRepository.save(entity);
    }

    /**
     * 获取保险映射ID
     */
    private String getHelloInsuranceMappingFromSaas(Long merchantId, Long storeId, Long chargeItem,
            String insuranceName) {
        byte idRelationType = IdRelationEnum.Vehicle.getTypeByDesc(insuranceName);
        return thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(HELLO, merchantId, storeId,
                        idRelationType, chargeItem)
                .map(ThirdVehicleIdRelationEntity::getThirdId)
                .orElse(null);
    }

    /**
     * 获取附加服务映射ID
     */
    private String getHelloAddedServiceMappingFromSaas(Long merchantId, Long storeId, Long chargeItem, String serviceName) {
        byte idRelationType =  IdRelationEnum.Vehicle.getTypeByDesc(serviceName);
        return thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(HELLO, merchantId, storeId,
                        idRelationType, chargeItem)
                .map(ThirdVehicleIdRelationEntity::getThirdId)
                .orElse(null);
    }

    /**
     * 保存或更新附加服务ID映射
     */
    private void saveOrUpdateAddedServiceMapping(Long merchantId, Long storeId, Byte chargeItem, String thirdId) {
        ThirdVehicleIdRelationEntity entity = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.ADDED_SERVICE.getType(), (long) chargeItem)
                .orElse(new ThirdVehicleIdRelationEntity());

        entity.setMerchantId(merchantId);
        entity.setStoreId(storeId);
        entity.setChannelId(HELLO);
        entity.setType(IdRelationEnum.Vehicle.ADDED_SERVICE.getType());
        entity.setThirdId(thirdId);
        entity.setSaasId((long) chargeItem);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setOpTime(System.currentTimeMillis());

        thirdVehicleIdRelationRepository.save(entity);
    }

    /**
     * 删除库存映射关系
     */
    private void deleteStockMapping(Long merchantId, Long stockId, String thirdStockId,
            ThirdVehicleIdRelationEntity entity) {
        try {
            entity.setDeleted(1);
            entity.setOpTime(System.currentTimeMillis());
            thirdVehicleIdRelationRepository.save(entity);

            Span.current().addEvent("删除库存映射关系成功", Attributes.builder()
                    .put("merchantId", merchantId)
                    .put("stockId", stockId)
                    .put("thirdStockId", thirdStockId)
                    .build());
        } catch (Exception e) {
            Span.current().addEvent("删除库存映射关系失败", Attributes.builder()
                    .put("merchantId", merchantId)
                    .put("stockId", stockId)
                    .put("error", e.getMessage())
                    .build());
        }
    }

    @Override
    @WithSpan("[hello]库存初始化")
    public SaasResultResp initStock(Long merchantId) {
        int totalStocks = 0;
        Span span = Span.current();
        try {
            span.setAttribute("merchantId", merchantId);
            // 先获取商户信息
            String thirdMerchantId;
            Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO);
            if (!apiConnOpt.isPresent()) {
                logger.error("[HELLO][HELLO-INIT]库存初始化异常: 商家不存在");
                this.initFailV2(merchantId, "商家不存在", "初始化错误:商家开通渠道信息丢失", 70);
                return SaasResultResp.failed("-1", "库存初始化异常: 商家不存在");
            }
            thirdMerchantId = apiConnOpt.get().getChannelVendorCode();
            span.setAttribute("thirdMerchantId", thirdMerchantId);

            // 查询所有需要初始化库存的车辆映射关系
            List<ThirdVehicleIdRelationEntity> thirdVehicleIdRelationList = thirdVehicleIdRelationRepository
                    .findByChannelIdAndMerchantIdAndType(HELLO, merchantId,
                            IdRelationEnum.Vehicle.VEHICLE_INFO.getType());

            int totalVehicles = thirdVehicleIdRelationList.size();
            logger.infov("[HELLO][HELLO-INIT]开始初始化库存, merchantId: {0}, 车辆总数: {1}", merchantId, totalVehicles);

            // 如果没有车辆映射关系，直接返回成功
            if (thirdVehicleIdRelationList.isEmpty()) {
                logger.infov("[HELLO][HELLO-INIT]没有需要初始化库存的车辆, merchantId: {0}", merchantId);
                this.initSuccessV2(merchantId, 70);
                return SaasResultResp.success();
            }

            for (ThirdVehicleIdRelationEntity thirdVehicleIdRelation : thirdVehicleIdRelationList) {

                // 获取车辆ID
                Long vehicleId = thirdVehicleIdRelation.getSaasId();
                String thirdVehicleId = thirdVehicleIdRelation.getThirdId();

                // 查询车辆库存占用情况 - 直接使用helloClientWrapper
                // 创建查询车辆库存占用请求
                QueryCarOccupyInventoryResp occupyInventory = getSurviveStockFromHello(merchantId, thirdMerchantId,
                        thirdVehicleId, vehicleId);

                // 获取库存占用信息
                if (occupyInventory.getOccupyList() == null || occupyInventory.getOccupyList().isEmpty()) {
                    logger.infov("[HELLO][HELLO-INIT]没有需要初始化的库存, merchantId: {0}, vehicleId: {1}",
                            merchantId, vehicleId);
                    continue;
                }

                // 获取车辆信息
                ApiResultResp<VehicleInfoDTO> vehicleInfoResp = saasVehicleClient.getVehicleDetailById(vehicleId);
                if (!ApiResultResp.isSuccess(vehicleInfoResp) || vehicleInfoResp.getData() == null) {
                    logger.errorv("获取车辆信息失败, merchantId: {0}, vehicleId: {1}",
                            merchantId, vehicleId);
                    // 失败时中止流程
                    this.initFailV2(merchantId, vehicleInfoResp.getMsg(), "库存初始化:获取车辆信息失败", 70);
                    return SaasResultResp.failed("-1", "获取车辆信息失败, vehicleId: " + vehicleId);
                }

                VehicleInfoDTO vehicleInfo = vehicleInfoResp.getData();

                // 提取所有占用ID
                List<String> occupyIds = HelloOccupyToVehicleBusyConverter
                        .extractOccupyIds(occupyInventory.getOccupyList());

                // 批量查询已存在的库存映射关系，做幂等检查
                List<ThirdVehicleIdRelationEntity> existingStockMappings = thirdVehicleIdRelationRepository
                        .findByChannelIdAndMerchantIdAndTypeAndThirdIdIn(
                                HELLO,
                                merchantId,
                                IdRelationEnum.Vehicle.STOCK.getType(),
                                occupyIds);

                // 提取已存在的占用ID集合
                Set<String> existingOccupyIds = HelloOccupyToVehicleBusyConverter
                        .extractExistingOccupyIds(existingStockMappings);

                Set<Long> existingSaasOccupyIds = HelloOccupyToVehicleBusyConverter
                        .extractExistingSaasOccupyIds(existingStockMappings);

                // 使用转换器将Hello平台的库存占用信息转换为Saas平台的库存请求
                HelloOccupyToVehicleBusyConverter converter = new HelloOccupyToVehicleBusyConverter(
                        merchantId,
                        HELLO,
                        vehicleInfo,
                        vehicleId,
                        existingOccupyIds);

                // 过滤掉已存在的占用ID 或者 非订单占用的库存
                final List<Integer> HELLO_STOCK_ORDER_TYPE = Arrays.asList(10,11,12);
                List<QueryCarOccupyInventoryResp.CarOccupyInfo> filteredOccupyList = occupyInventory.getOccupyList().stream()
                    .filter(info -> !existingOccupyIds.contains(String.valueOf(info.getGuid())) && HELLO_STOCK_ORDER_TYPE.contains(info.getOccupiedType()))
                    .collect(Collectors.toList());

                // 批量转换库存占用信息
                List<VehicleBusyReq> vehicleBusyReqList = converter.convertBatch(filteredOccupyList);

                // 删除已有的哈啰库存
                ApiResultResp<List<VehicleBusyDTO>> saasStockResp =
                        saasVehicleClient.getStockByVehicleId(merchantId, vehicleId, System.currentTimeMillis());
                logger.infov("[HELLO][HELLO-INIT]获取saas车辆库存占用: merchantId: {0}, vehicleId: {1}, result: {2}",
                        merchantId, vehicleId, saasStockResp);
                if (ApiResultResp.isSuccess(saasStockResp) && saasStockResp.getData() != null) {
                    List<Long> deleteSaasIds = saasStockResp.getData().stream()
                            .filter(stock -> HELLO.equals(stock.getChannelId()) && !existingSaasOccupyIds.contains(stock.getId()))
                            .map(VehicleBusyDTO::getId).collect(Collectors.toList());

                    if (!deleteSaasIds.isEmpty()) {
                        // 批量删除哈啰爬虫的库存
                        SaasResponse<Boolean> saasResponse = saasVehicleClient.busyDelete(merchantId, deleteSaasIds);
                        logger.infov("[HELLO][HELLO-INIT]批量删除哈啰爬虫库存, merchantId: {0}, deleteSaasIds: {1}, result: {2}",
                                merchantId, deleteSaasIds, SpanUtil.toJson(saasResponse));
                    }
                }

                // 如果没有库存请求，跳过
                if (vehicleBusyReqList.isEmpty()) {
                    logger.infov("[HELLO][HELLO-INIT]没有需要初始化的库存或所有库存已存在, merchantId: {0}, vehicleId: {1}",
                            merchantId, vehicleId);
                    continue;
                }

                // 调用saas批量保存单个车辆的库存
                SaasResponse<List<VehicleBusyResp>> busyRespSaasResponse = saasVehicleClient.busyCreateBatch(merchantId,
                        vehicleBusyReqList);
                if (busyRespSaasResponse == null || !SaasResponse.SUCCESS_CODE.equals(busyRespSaasResponse.getCode())
                        || busyRespSaasResponse.getData() == null) {
                    logger.errorv("[HELLO][HELLO-INIT]批量保存库存失败, merchantId: {0}, vehicleId: {1}, errorMsg: {2}",
                            merchantId, vehicleId,
                            busyRespSaasResponse != null ? busyRespSaasResponse.getMessage() : "响应为空");
                    // 失败时中止流程
                    this.initFailV2(merchantId, busyRespSaasResponse.getMessage(), "库存初始化:保存库存失败", 70);
                    return SaasResultResp.failed("-1",
                            "批量保存库存失败: " + (busyRespSaasResponse != null ? busyRespSaasResponse.getMessage() : "响应为空"));
                }

                // 批量保存库存id映射关系
                List<VehicleBusyResp> busyRespList = busyRespSaasResponse.getData();

                // 批量保存库存映射关系
                List<ThirdVehicleIdRelationEntity> stockMappingsToSave = new ArrayList<>();
                for (VehicleBusyResp resp : busyRespList) {
                    // 使用key字段作为thirdId，key中存储的是occupyInfo.getGuid()
                    String guid = resp.getKey();
                    if (guid != null) {
                        // 创建映射实体
                        ThirdVehicleIdRelationEntity entity = new ThirdVehicleIdRelationEntity();
                        entity.setMerchantId(merchantId);
                        entity.setStoreId(resp.getStoreId());
                        entity.setChannelId(HELLO);
                        entity.setType(IdRelationEnum.Vehicle.STOCK.getType());
                        entity.setThirdId(guid);
                        entity.setSaasId(resp.getId());
                        entity.setCreateTime(System.currentTimeMillis());
                        entity.setOpTime(System.currentTimeMillis());

                        stockMappingsToSave.add(entity);
                    }
                }

                // 批量保存映射关系
                if (!stockMappingsToSave.isEmpty()) {
                    thirdVehicleIdRelationRepository.saveAll(stockMappingsToSave);
                    totalStocks += stockMappingsToSave.size();
                }
            }

            // 再重新覆盖推送hello库存
            for (ThirdVehicleIdRelationEntity thirdVehicleIdRelation : thirdVehicleIdRelationList) {
                PushStockByVehicleReq pushStockReq = new PushStockByVehicleReq();
                pushStockReq.setVehicleId(thirdVehicleIdRelation.getSaasId());
                pushStockReq.setMerchantId(merchantId);
                SaasResultResp saasResultResp = pushStockByVehicle(pushStockReq);
                if (!saasResultResp.isSuccess()) {
                    logger.errorv("[HELLO][HELLO-INIT]重新覆盖推送hello库存失败, merchantId: {0}, vehicleId: {1}, errorMsg: {2}",
                            merchantId, thirdVehicleIdRelation.getSaasId(), saasResultResp.getMsg());
                    // 推送失败不停止
//                    this.initFailV2(merchantId, saasResultResp.getMsg(), "推送哈啰库存失败", 70);
                }
            }

            logger.infov("[HELLO][HELLO-INIT]库存初始化完成, merchantId: {0}, 总库存数: {1}",
                    merchantId, totalStocks);

            this.initSuccessV2(merchantId, 70);
            return SaasResultResp.success();
        } catch (BizException e) {
            this.initFailV2(merchantId, e.getMessage(), "库存初始化错误", 70);
            return SaasResultResp.failed("-1", e.getMessage());
        } catch (Exception e) {
            span.recordException(e);
            this.initFailV2(merchantId, e.getMessage(), "库存初始化错误", 70);
            logger.errorv("[HELLO][HELLO-INIT]库存初始化异常, merchantId: {0}, error: {1}", merchantId, e);
            return SaasResultResp.failed("-1", "库存初始化异常: " + e.getMessage());
        }
    }

    /**
     * 获取哈啰有效库存
     *
     * @param merchantId
     * @param thirdMerchantId
     * @param thirdVehicleId
     * @param vehicleId
     * @return
     */
    private QueryCarOccupyInventoryResp getSurviveStockFromHello(Long merchantId, String thirdMerchantId,
            String thirdVehicleId, Long vehicleId) {
        CarOccupyInventoryQueryReq req = new CarOccupyInventoryQueryReq();
        req.setMerchantId(thirdMerchantId);
        // 设置起始时间为当前时间的前一天 原因：哈啰接口查不到当天库存
        LocalDateTime now = LocalDateTime.now().withNano(0);
        LocalDateTime yesterday = now.minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        req.setStartTime(yesterday.format(formatter));
        req.setEndTime("3999-12-31");
        req.setCarIdList(Collections.singletonList(thirdVehicleId));

        // 调用HelloClientWrapper查询车辆库存占用
        ResultResp<QueryCarOccupyInventoryResp> resultResp = helloClientWrapper.queryOccupyInventory(merchantId, req);

        if (!ResultResp.isSuccess(resultResp) || resultResp.getData() == null) {
            logger.errorv("[HELLO][HELLO-INIT]查询hello车辆库存占用失败, merchantId: {0}, vehicleId: {1}",
                    merchantId, vehicleId);
            throw new BizException(ClientName.HELLO, "-1", "查询hello车辆库存占用失败");
        }
        QueryCarOccupyInventoryResp occupyInventory = resultResp.getData();
        // 过滤库存释放时间小于当前时间的库存占用
        if (occupyInventory.getOccupyList() != null) {
            occupyInventory.getOccupyList().removeIf(occupy -> {
                LocalDateTime releaseTime = LocalDateTime.parse(occupy.getActualReleased(),
                        DateTimeFormatter.ISO_DATE_TIME);
                return releaseTime.isBefore(now);
            });
        }
        return occupyInventory;
    }


    @Override
    public void initSuccessV2(Long merchantId, Integer index) {
        TaskStatusUpdateReq req = new TaskStatusUpdateReq();
        req.setMerchantId(merchantId);
        req.setStatus(1);
        req.setChannelId(HELLO);
        req.setIndex(index);
        saasClient.initCallBack(req);
    }

    @Override
    public void initFailV2(Long merchantId, String message, String displayMessage, Integer index) {
        TaskStatusUpdateReq req = new TaskStatusUpdateReq();
        req.setMerchantId(merchantId);
        req.setDetailMessage(message);
        req.setDisplayMessage(displayMessage);
        req.setStatus(2);
        req.setChannelId(HELLO);
        req.setIndex(index);
        saasClient.initCallBack(req);
    }

    /**
     * 获取车辆映射ID
     */
    private String getHelloVehicleMappingFromSaas(Long merchantId, Long vehicleId) {
        return thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.VEHICLE_INFO.getType(), vehicleId)
                .map(ThirdVehicleIdRelationEntity::getThirdId)
                .orElse(null);
    }

    @Override
    @WithSpan("[hello]车辆维度库存全量推送")
    public SaasResultResp pushStockByVehicle(PushStockByVehicleReq req) {
        Span span = Span.current();
        try {
            if (req == null || req.getVehicleId() == null || req.getMerchantId() == null) {
                logger.errorv("全量库存推送参数错误 req: {0}", req);
                return SaasResultResp.failed("-1", "参数错误");
            }

            Long merchantId = req.getMerchantId();
            Long vehicleId = req.getVehicleId();
            span.setAttribute("merchantId", merchantId);
            span.setAttribute("vehicleId", vehicleId);

            // 1. 获取车辆mapping
            String thirdVehicleId = getHelloVehicleMappingFromSaas(merchantId, vehicleId);
            if (thirdVehicleId == null) {
                logger.errorv("[HELLO][HELLO-INIT]未找到对应的hello车辆id映射关系, vehicleId: {0}", vehicleId);
                return SaasResultResp.failed("-1", "未找到对应的hello车辆id映射关系");
            }
            span.setAttribute("thirdVehicleId", thirdVehicleId);

            // 获取车辆信息
            ApiResultResp<VehicleInfoDTO> vehicleInfoResp = saasVehicleClient.getVehicleDetailById(vehicleId);
            if (!ApiResultResp.isSuccess(vehicleInfoResp) || vehicleInfoResp.getData() == null) {
                logger.errorv("[HELLO][HELLO-INIT]获取车辆信息失败, vehicleId: {0}", vehicleId);
                return SaasResultResp.failed("-1", "获取车辆信息失败");
            }
            Long storeId = vehicleInfoResp.getData().getSaasStoreId();

            String thirdMerchantId;
            Optional<ApiConnEntity> apiConnOpt = apiConnRepository.findByMerchantIdAndChannelId(merchantId, HELLO);
            if (apiConnOpt.isEmpty()) {
                logger.errorv("[HELLO][HELLO-INIT]未找到对应的哈啰商家id映射关系, merchantId: {0}", merchantId);
                return SaasResultResp.failed("-1", "未找到对应的哈啰商家id映射关系");
            }
            thirdMerchantId = apiConnOpt.get().getChannelVendorCode();
            span.setAttribute("thirdMerchantId", thirdMerchantId);
            // 2. 获取车辆所有有效库存 获取哈啰系统中的库存占用
            QueryCarOccupyInventoryResp surviveStockFromHello = getSurviveStockFromHello(merchantId, thirdMerchantId,
                    thirdVehicleId, vehicleId);

            // 获取哈啰系统中的占用列表
            List<QueryCarOccupyInventoryResp.CarOccupyInfo> helloOccupyList = Optional
                    .ofNullable(surviveStockFromHello.getOccupyList())
                    .orElseGet(ArrayList::new);

            // 3. 获取saas系统中的库存占用
            ApiResultResp<List<VehicleBusyDTO>> saasStockResp = saasVehicleClient.getStockByVehicleId(merchantId,
                    vehicleId, System.currentTimeMillis());
            if (!ApiResultResp.isSuccess(saasStockResp) || saasStockResp.getData() == null) {
                logger.errorv("获取saas车辆库存占用失败: {0}, vehicleId: {1}",
                        saasStockResp != null ? saasStockResp.getMsg() : "响应为空", vehicleId);
                return SaasResultResp.failed("-1", "获取saas车辆库存占用失败: " +
                        (saasStockResp != null ? saasStockResp.getMsg() : "响应为空"));
            }

            // 过滤掉sourceType=1（订单）且channelId=4（哈啰渠道）的库存，这些库存不需要推送
            Map<Long, VehicleBusyDTO> saasStockMap = saasStockResp.getData().stream()
                    .filter(busy -> !(busy.getSourceType() != null && busy.getSourceType() == 1
                            && busy.getChannelId() != null && busy.getChannelId() == 4))
                    .collect(Collectors.toMap(
                            VehicleBusyDTO::getId,
                            Function.identity(),
                            (o1, o2) -> o1));

            // 4-5. 一次遍历获取所需的ID集合
            Set<String> orderOccupyIds = new HashSet<>();
            Set<String> orderIds = new HashSet<>();
            Set<String> guids = new HashSet<>();
            
            for (QueryCarOccupyInventoryResp.CarOccupyInfo info : helloOccupyList) {
                Integer occupiedType = info.getOccupiedType();
                boolean isOrderOccupy = occupiedType != null && (occupiedType == 10 || occupiedType == 11 || occupiedType == 12);
                
                if (isOrderOccupy) {
                    // 订单占用的库存，只记录其guid
                    orderOccupyIds.add(String.valueOf(info.getGuid()));
                } else {
                    // 非订单占用的库存，记录orderId（如果有）和guid用于映射查找
                    if (info.getOrderId() != null) {
                        orderIds.add(String.valueOf(info.getOrderId()));
                    }
                    guids.add(String.valueOf(info.getGuid()));
                }
            }

            // 库存映射关系
            List<ThirdVehicleIdRelationEntity> mappingList = new ArrayList<>();
            
            // 先用orderId查找映射关系
            if (!orderIds.isEmpty()) {
                List<ThirdVehicleIdRelationEntity> orderMappings = thirdVehicleIdRelationRepository
                        .list("channelId = ?1 and type = ?2 and thirdId in ?3 and merchantId = ?4 and deleted = 0",
                                ChannelEnum.HELLO.getCode(),
                                IdRelationEnum.Vehicle.STOCK.getType(),
                                orderIds,
                                merchantId);
                logger.infov("[HELLO][HELLO-INIT]通过orderId查找到库存映射关系数量: {0}, merchantId: {1}, vehicleId: {2}", 
                        orderMappings.size(), merchantId, vehicleId);
                mappingList.addAll(orderMappings);
            }
            
            // 再用guid查找映射关系（排除已经通过orderId找到的）
            Set<String> foundThirdIds = mappingList.stream()
                    .map(ThirdVehicleIdRelationEntity::getThirdId)
                    .collect(Collectors.toSet());
            // 同时也要排除已经有相同saasId映射的guid
            Set<Long> foundSaasIds = mappingList.stream()
                    .map(ThirdVehicleIdRelationEntity::getSaasId)
                    .collect(Collectors.toSet());
            Set<String> remainingGuids = guids.stream()
                    .filter(guid -> !foundThirdIds.contains(guid))
                    .collect(Collectors.toSet());
                    
            if (!remainingGuids.isEmpty()) {
                List<ThirdVehicleIdRelationEntity> guidMappings = thirdVehicleIdRelationRepository
                        .list("channelId = ?1 and type = ?2 and thirdId in ?3 and merchantId = ?4 and deleted = 0",
                                ChannelEnum.HELLO.getCode(),
                                IdRelationEnum.Vehicle.STOCK.getType(),
                                remainingGuids,
                                merchantId);
                // 过滤掉与已有orderId映射相同saasId的guid映射，避免重复
                List<ThirdVehicleIdRelationEntity> filteredGuidMappings = guidMappings.stream()
                        .filter(mapping -> !foundSaasIds.contains(mapping.getSaasId()))
                        .collect(Collectors.toList());
                logger.infov("[HELLO][HELLO-INIT]通过guid查找到库存映射关系数量: {0}, 过滤后: {1}, merchantId: {2}, vehicleId: {3}", 
                        guidMappings.size(), filteredGuidMappings.size(), merchantId, vehicleId);
                mappingList.addAll(filteredGuidMappings);
            }

            // 构建哈啰库存ID到映射的Map
            Map<String, ThirdVehicleIdRelationEntity> helloStockIdMappingMap = mappingList.stream()
                    .filter(mapping -> mapping.getThirdId() != null && mapping.getSaasId() != null)
                    .collect(Collectors.toMap(
                            ThirdVehicleIdRelationEntity::getThirdId,
                            Function.identity(),
                            (o1, o2) -> o1));
            
            logger.infov("[HELLO][HELLO-INIT]总库存映射关系数量: {0}, merchantId: {1}, vehicleId: {2}", 
                    helloStockIdMappingMap.size(), merchantId, vehicleId);

            // 构建saas库存ID到映射的Map (从同一个mappingList数据构建)
            Map<Long, ThirdVehicleIdRelationEntity> saasStockIdMappingMap = mappingList.stream()
                    .filter(mapping -> mapping.getSaasId() != null)
                    .collect(Collectors.toMap(
                            ThirdVehicleIdRelationEntity::getSaasId,
                            Function.identity(),
                            (o1, o2) -> o1));

            // 6. 处理哈啰端库存
            Set<String> helloStockToDeleteIds = new HashSet<>();
            // 建立需要删除的saasStockId集合，用于后续判断是否需要重新推送
            Set<Long> saasStockIdsToDelete = new HashSet<>();

            for (QueryCarOccupyInventoryResp.CarOccupyInfo helloOccupy : helloOccupyList) {
                String helloStockId = String.valueOf(helloOccupy.getGuid());
                // 跳过哈啰订单占用的库存
                if (orderOccupyIds.contains(helloStockId)) {
                    continue;
                }

                // 先尝试用orderId查找映射关系，再用guid查找
                ThirdVehicleIdRelationEntity mapping = null;
                if (helloOccupy.getOrderId() != null) {
                    mapping = helloStockIdMappingMap.get(String.valueOf(helloOccupy.getOrderId()));
                }
                if (mapping == null) {
                    mapping = helloStockIdMappingMap.get(helloStockId);
                }
                
                if (mapping == null) {
                    // 没有映射关系的非订单库存，需要删除
                    helloStockToDeleteIds.add(helloStockId);
                } else {
                    // 有映射关系，检查时间是否一致
                    Long saasStockId = mapping.getSaasId();
                    VehicleBusyDTO saasStock = saasStockMap.get(saasStockId);

                    if (saasStock == null) {
                        // saas中找不到对应库存，需要删除
                        helloStockToDeleteIds.add(helloStockId);
                        saasStockIdsToDelete.add(saasStockId);
                    } else {
                        // 使用LocalDateTime比较开始和结束时间是否一致
                        try {
                            if (helloOccupy.getOccupiedStart() == null || helloOccupy.getOccupiedEnd() == null) {
                                logger.warnv("[HELLO][HELLO-INIT]哈啰库存占用时间为空，需要删除重建, vehicleId: {0}, helloStockId: {1}", 
                                        vehicleId, helloStockId);
                                helloStockToDeleteIds.add(helloStockId);
                                saasStockIdsToDelete.add(saasStockId);
                            } else {
                                LocalDateTime helloStartTime = LocalDateTime.parse(helloOccupy.getOccupiedStart());
                                LocalDateTime helloEndTime = LocalDateTime.parse(helloOccupy.getOccupiedEnd());
                                
                                if (saasStock.getStartTime() == null || saasStock.getEndIntervalTime() == null) {
                                    logger.warnv("[HELLO][HELLO-INIT]SAAS库存时间为空，需要删除重建, vehicleId: {0}, saasStockId: {1}", 
                                            vehicleId, saasStockId);
                                    helloStockToDeleteIds.add(helloStockId);
                                    saasStockIdsToDelete.add(saasStockId);
                                } else {
                                    LocalDateTime saasStartTime = LocalDateTime
                                            .ofInstant(Instant.ofEpochMilli(saasStock.getStartTime()), ZoneId.systemDefault())
                                            .withNano(0);
                                    LocalDateTime saasEndTime = LocalDateTime
                                            .ofInstant(Instant.ofEpochMilli(saasStock.getEndIntervalTime()), ZoneId.systemDefault())
                                            .withNano(0);

                                    if (!helloStartTime.isEqual(saasStartTime) || !helloEndTime.isEqual(saasEndTime)) {
                                        // 时间不一致，需要删除后重新推送
                                        helloStockToDeleteIds.add(helloStockId);
                                        saasStockIdsToDelete.add(saasStockId);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.warnv("[HELLO][HELLO-INIT]解析哈啰库存占用时间失败，需要删除重建, vehicleId: {0}, helloStockId: {1}, error: {2}", 
                                    vehicleId, helloStockId, e.getMessage());
                            helloStockToDeleteIds.add(helloStockId);
                            saasStockIdsToDelete.add(saasStockId);
                        }
                    }
                }
            }

            // 7. 处理saas端库存，找出需要推送到哈啰的库存
            List<CarOccupyInventoryReq> vehicleBusyReqList = new ArrayList<>();
            // 与vehicleBusyReqList一一对应的saasStockId列表
            List<Long> saasStockIdList = new ArrayList<>();

            // 创建哈啰转换器
            HelloVehicleBusyConverter converter = new HelloVehicleBusyConverter(thirdMerchantId, thirdVehicleId);

            // 8. 遍历SAAS库存，找出需要新增的库存
            for (VehicleBusyDTO saasStock : saasStockMap.values()) {
                Long saasStockId = saasStock.getId();

                // 检查是否有映射关系
                ThirdVehicleIdRelationEntity mapping = saasStockIdMappingMap.get(saasStockId);

                // 需要推送的情况：1.没有映射关系 2.映射关系存在但对应的库存需要删除重建
                if (mapping == null || saasStockIdsToDelete.contains(saasStockId)) {
                    CarOccupyInventoryReq busyReq = converter.convert(saasStock);
                    vehicleBusyReqList.add(busyReq);
                    saasStockIdList.add(saasStockId);
                }
            }

            // 9. 执行哈啰库存删除（逐个删除）
            for (String helloStockId : helloStockToDeleteIds) {
                CarReleaseInventoryReq releaseReq = new CarReleaseInventoryReq();
                List<Long> occupyIdList = new ArrayList<>();
                occupyIdList.add(Long.valueOf(helloStockId));
                releaseReq.setOccupyIdList(occupyIdList);
                releaseReq.setMerchantId(thirdMerchantId);
                releaseReq.setTemp(true);
                releaseReq.setCarId(thirdVehicleId);

                ResultResp<CarReleaseInventoryResp> deleteResp = releaseInventoryWithRetry(merchantId, releaseReq);
                if (!deleteResp.isSuccess() || !deleteResp.getData().getResult()) {
                    logger.warnv("[HELLO][HELLO-INIT]删除哈啰库存失败: {0}, vehicleId: {1}, helloStockId: {2}, releaseReq:{3}",
                            deleteResp.getMsg(), vehicleId, helloStockId, SpanUtil.toJson(releaseReq));
                } else {
                    // 删除库存映射关系 - 需要根据helloStockId（guid）找到对应的占用信息，然后查找映射
                    QueryCarOccupyInventoryResp.CarOccupyInfo occupyInfo = helloOccupyList.stream()
                            .filter(info -> String.valueOf(info.getGuid()).equals(helloStockId))
                            .findFirst()
                            .orElse(null);
                    
                    ThirdVehicleIdRelationEntity mapping = null;
                    if (occupyInfo != null) {
                        // 先尝试用orderId查找，再用guid查找
                        if (occupyInfo.getOrderId() != null) {
                            mapping = helloStockIdMappingMap.get(String.valueOf(occupyInfo.getOrderId()));
                        }
                        if (mapping == null) {
                            mapping = helloStockIdMappingMap.get(helloStockId);
                        }
                    }
                    
                    if (mapping != null) {
                        // 逻辑删除
                        thirdVehicleIdRelationRepository.logicalDeleteByIds(Collections.singletonList(mapping.getId()));
                    }
                }
            }

            // 10. 执行哈啰库存新增
            if (vehicleBusyReqList.isEmpty()) {
                return SaasResultResp.success();
            }

            for (int i = 0; i < vehicleBusyReqList.size(); i++) {
                CarOccupyInventoryReq busyReq = vehicleBusyReqList.get(i);
                Long saasStockId = saasStockIdList.get(i);

                ResultResp<CarOccupyInventoryResp> busyResp = helloClientWrapper.occupyInventoryNotify(merchantId,
                        busyReq);
                if (!busyResp.isSuccess() || busyResp.getData() == null || !busyResp.getData().getPreOccupyResult()
                        || busyResp.getData().getOccupyId() == null) {
                    logger.warnv("[HELLO][HELLO-INIT]推送哈啰库存占用失败: {0}, vehicleId: {1}",
                            busyResp.getMsg(), vehicleId);
                } else {
                    // 新增库存映射关系
                    Long helloStockId = busyResp.getData().getOccupyId();
                    saveOrUpdateStockMapping(merchantId, storeId, saasStockId, String.valueOf(helloStockId));
                }
            }
            return SaasResultResp.success();
        } catch (Exception e) {
            logger.errorv("[HELLO][HELLO-INIT]全量库存推送异常: {0}, vehicleId: {1}", e.getMessage(), req.getVehicleId(), e);
            span.setStatus(StatusCode.ERROR);
            span.recordException(e);
            return SaasResultResp.failed("-1", e.getMessage());
        }
    }

    /**
     * 车辆排车
     *
     * @param thirdMerchantId 渠道商户码
     * @param merchantId      商家ID
     * @param req             排车请求参数
     * @return 排车操作结果
     */
    @Override
    @WithSpan("[hello]车辆排车")
    public SaasResultResp vehicleSettle(String thirdMerchantId, Long merchantId, VehicleSettleReq req) {
        Span span = Span.current();
        span.setAttribute("thirdMerchantId", thirdMerchantId);
        span.setAttribute("merchantId", merchantId);
        span.setAttribute("req", SpanUtil.toJson(req));

        // 先做check 比如先查出mapping，没有mapping则return fail
        // 查询门店mapping
        ThirdIdRelationEntity storeRelation = thirdIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Store.STORE.getType(), req.getStoreId())
                .orElse(null);
        if (storeRelation == null) {
            return SaasResultResp.failed("-1", "未找到门店映射关系");
        }

        // 查询商品mapping
        String thirdGoodsId = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndStoreIdAndTypeAndSaasId(HELLO, merchantId, req.getStoreId(),
                        IdRelationEnum.Vehicle.VEHICLE_MODEL.getType(), req.getVehicleModelId())
                .map(ThirdVehicleIdRelationEntity::getThirdId)
                .orElse(null);
        if (thirdGoodsId == null) {
            return SaasResultResp.failed("-1", "未找到车型(商品)映射关系");
        }

        // 查询车辆mapping
        ThirdVehicleIdRelationEntity vehicleRelation = thirdVehicleIdRelationRepository
                .findByChannelIdAndMerchantIdAndTypeAndSaasId(HELLO, merchantId,
                        IdRelationEnum.Vehicle.VEHICLE_INFO.getType(), req.getVehicleId())
                .orElse(null);
        if (vehicleRelation == null) {
            return SaasResultResp.failed("-1", "未找到车辆映射关系");
        }

        // 使用转换器转换排车请求
        HelloVehicleSettleConverter converter = new HelloVehicleSettleConverter(
                thirdMerchantId,
                storeRelation.getThirdId(),
                thirdGoodsId,
                vehicleRelation.getThirdId());
        HelloVehicleSettleReq settleReq = converter.convert(req);

        ResultResp<VehicleSettleResp> resultResp = helloClientWrapper.vehicleSettleNotify(thirdMerchantId, settleReq);
        if (resultResp.isSuccess() && resultResp.getData() != null && resultResp.getData().getResult() != null
                && resultResp.getData().getResult()) {
            return SaasResultResp.success();
        } else {
            span.setStatus(StatusCode.ERROR);
            span.setAttribute("errorCode", resultResp.getCode());
            span.setAttribute("errorMsg", resultResp.getMsg());
            return SaasResultResp.failed(resultResp.getCode(), resultResp.getMsg());
        }
    }

    /**
     * 释放车辆库存占用（带重试逻辑）
     * 注意：此方法不支持批量删除库存，每次只能删除单个库存
     *
     * @param merchantId 商户ID
     * @param req        释放请求
     * @return 接口响应
     */
    private ResultResp<CarReleaseInventoryResp> releaseInventoryWithRetry(Long merchantId, CarReleaseInventoryReq req) {
        // 检查是否为批量删除，如果是则抛出异常
        if (req.getOccupyIdList() != null && req.getOccupyIdList().size() > 1) {
            logger.errorv("不支持批量删除库存，occupyIdList size: {0}", req.getOccupyIdList().size());
            throw new IllegalArgumentException("不支持批量删除库存");
        }
        
        // 带temp=true的删除
        ResultResp<CarReleaseInventoryResp> carReleaseInventoryResult = helloClientWrapper.releaseInventoryNotify(merchantId, req);
        logger.infov("请求hello接口报文 释放车辆库存占用 带temp数据, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(carReleaseInventoryResult));

        // 删除接口返回成功，直接返回
        if (carReleaseInventoryResult.isSuccess() && carReleaseInventoryResult.getData() != null
                && carReleaseInventoryResult.getData().getResult() != null
                && carReleaseInventoryResult.getData().getResult()) {
            return carReleaseInventoryResult;
        }

        // 兼容历史数据 不带temp的删除
        req.setTemp(null);
        req.setCarId(null);
        ResultResp<CarReleaseInventoryResp> carReleaseInventoryResultWithNotTemp = helloClientWrapper.releaseInventoryNotify(merchantId, req);
        logger.infov("请求hello接口报文 释放车辆库存占用 不带temp数据, req:{0}, resp:{1}", SpanUtil.toJson(req), SpanUtil.toJson(carReleaseInventoryResultWithNotTemp));

        // 返回最后一次的结果
        return carReleaseInventoryResultWithNotTemp;
    }

}
