package com.qinglusaas.connect.infra.remote.hello.vo.reponse;

import com.qinglusaas.connect.client.hello.constants.ErrorCode;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
@Data
public class ResultResp<T> {

    /**
     * 平台颁发的每次请求访问的唯一标识
     */
    private String traceId;
    /**
     * 请求失败返回的错误码
     */
    private String code;
    /**
     * 请求失败返回的错误信息
     */
    private String msg;
    /**
     * 请求失败返回的子错误码
     */
    private String subCode;
    /**
     * 请求失败返回的子错误信息
     */
    private String subMsg;
    /**
     * 业务响应信息
     */
    private T data;

    public boolean isSuccess() {
        return ErrorCode.SUCCESS.getCode().equals(code);
    }

    public boolean isFailed() {
        return !isSuccess();
    }

    public boolean isTokenInvalid() {
        return ErrorCode.INVALID_TOKEN_FAIL.getCode().equalsIgnoreCase(code);
    }

    public static boolean isSuccess(ResultResp resultResp) {
        return resultResp != null && resultResp.isSuccess();
    }
}
