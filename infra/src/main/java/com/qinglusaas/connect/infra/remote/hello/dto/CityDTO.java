package com.qinglusaas.connect.infra.remote.hello.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CityDTO {

    /**
     * 城市编号(电话区号)  eg：0517
     */
    private String cityCode;
    /**
     * 城市名称
     */
    private String cityName;

}
