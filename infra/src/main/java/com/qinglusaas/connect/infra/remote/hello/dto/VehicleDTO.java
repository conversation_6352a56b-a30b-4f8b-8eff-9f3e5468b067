package com.qinglusaas.connect.infra.remote.hello.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VehicleDTO {

    /**
     * 车型编号
     */
    String vehicleCode;

    /**
     * 车型名称
     */
    String vehicleName;

    /**
     * 哈啰车型年款code
     */
    String vehicleModelCode;

    /**
     * 1, "京牌"
     * 2, "沪牌"
     * 3, "粤A牌"
     * 4, "深牌"
     * 5, "浙A牌"
     * 6, "甘牌"
     * 7, "粤B牌"
     * 8, "非京牌"
     * 9, "非深牌"
     * 10, "非甘牌"
     * 11, "其他"
     */
    Integer licenceType;

}
