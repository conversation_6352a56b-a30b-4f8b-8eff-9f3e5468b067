package com.qinglusaas.connect.infra.remote.hello.vo.reponse;

import lombok.Data;

import java.util.List;

/**
 * 车辆库存占用查询响应
 */
@Data
public class QueryCarOccupyInventoryResp {
    /**
     * 车辆占用列表
     */
    private List<CarOccupyInfo> occupyList;

    /**
     * 车辆占用信息
     */
    @Data
    public static class CarOccupyInfo {
        /**
         * 占用ID
         */
        private Long guid;
        
        /**
         * 占用开始时间
         */
        private String occupiedStart;
        
        /**
         * 占用结束时间
         */
        private String occupiedEnd;
        
        /**
         * 车辆ID
         */
        private Long carId;
        
        /**
         * 库存释放时间
         */
        private String actualReleased;
        
        /**
         * 商品ID
         */
        private Long goodsId;
        
        /**
         * 占用类型(10,11,12属于订单占用)
         */
        private Integer occupiedType;
        
        /**
         * 订单ID
         */
        private Long orderId;
    }

    public static class QueryCarOccupyInventoryResult extends ResultResp<QueryCarOccupyInventoryResp> {}
} 