package com.qinglusaas.connect.infra.remote.hello.vo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.qinglusaas.connect.infra.remote.hello.constants.OrderStatusEnum;
import lombok.Data;

/**
 * 用于供应商推送订单状态到哈啰
 *
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/14, Monday
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CallbackOrderStatusReq extends BaseReq {

    /**
     * 第三方供应商单号
     * Y
     */
    private String thirdOrderNo;

    /**
     * 哈啰订单号
     * Y
     */
    private String helloOrderNo;

    /**
     * 订单回调状态code
     * PICKUPED：已取车，
     * CANCELLED：取消订单，
     * COMPLETED：已完成/已还车
     * Y
     */
    private OrderStatusEnum statusCode;

    /**
     * 取还车时间
     * N
     */
    private String pickUpDropOffTime;

    /**
     * 违约金金额
     * N
     */
    private Integer penaltyAmount;

}
