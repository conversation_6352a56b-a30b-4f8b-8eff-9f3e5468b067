package com.qinglusaas.connect.infra.remote.hello.vo.request;

import com.qinglusaas.connect.infra.remote.hello.dto.AddPurchaseItemDTO;
import com.qinglusaas.connect.infra.remote.hello.dto.ServiceGoodsPrice;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data

public class AddedServiceModifyNotifyReq extends BaseReq{
    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 渠道saas code
     */
    private String channelSaasCode;

    /**
     * 附加服务id
     */
    private String addPurchaseId;

    /**
     * 附加服务商品名称
     */
    private String addPurchaseName;

    /**
     * 附加服务商品类型
     */
    private Integer addPurchaseType;

    private AddPurchaseItemDTO addPurchaseItemDTO;

    private ServiceGoodsPrice serviceGoodsPrice;

}