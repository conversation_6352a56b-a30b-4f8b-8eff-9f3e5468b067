package com.qinglusaas.connect.infra.remote.hello.vo.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商品&价格配置
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HelloGoodsPriceSaveReq extends BaseReq {
    /**
     * 商户ID。
     */
    private String merchantId;
    /**
     * 商品基础信息
     */
    private GoodsBasic goodsBasic;
    /**
     * 价格配置
     */
    private List<Price> price;
    /**
     * 数据密级S1,渠道价格配置json
     */
    private List<Price> goodsChannelPriceDTOS;
    /**
     * 商品标签id
     */
    private List<Label> label;
    /**
     * 数据密级S1,商品增值服务id json
     */
    private List<Long> goodsServiceIds;
    /**
     * 数据密级S1,商品模版id json
     */
    private List<Integer> goodsTemplateIds;
    /**
     * 押金信息
     */
    private List<GoodsFee> goodsFee;
    /**
     * 商品组id
     */
    private Integer goodsGroupId;
    /**
     * 商品关联的增值服务价格表
     */
    private List<ServiceGoods> serviceGoods;
    /**
     * 渠道增值服务价格表
     */
    private List<ServiceGoods> channelServiceGoods;
    /**
     * 灰度商户标识
     */
    private Boolean grayMerchant;
    /**
     * 日志id（跨渠道使用）
     */
    private Object priceLogMap;
    /**
     * 异地配置
     */
    private List<GoodsRatesDiffConfig> goodsRatesDiffConfig;
    /**
     * 编辑标识
     */
    private Integer pageType;

    @Data
    public static class Price {
        /**
         * 商品id
         */
        private Long goodsId;
        /**
         * 价格id
         */
        private Integer id;
        /**
         * 维度类型
         */
        private Integer dimensionType;
        /**
         * 价格
         */
        private Long price;
        /**
         * 周中周末值
         */
        private List<Integer> weekdayList;
        /**
         * 自定义时间段
         */
        private TimePeriod timePeriod;
        /**
         *  数据密级S1,创建时间
         */
        private String createTime;
        /**
         * 数据密级S1,最近更新时间
         */
        private String updateTime ;
    }

    @Data
    public static class TimePeriod {
        /**
         * 结束时间
         */
        private String endDate ;
        /**
         * 开始时间
         */
        private String startDate;
    }

    @Data
    public static class Label {
        /**
         * 标签类型 目前就静态0
         */
        private Integer labelType;
        /**
         * 标签code
         */
        private Integer labelCode;
        /**
         * 商品标签id
         */
        private Integer labelId;
        /**
         * 商品标签名称
         */
        private String labelName;
        /**
         *  数据密级S1,创建时间
         */
        private String createTime;
        /**
         * 动态启用状态
         */
        private Integer statusEnable;
        /**
         * 数据密级S1,最近更新时间
         */
        private String updateTime ;
    }

    @Data
    public static class GoodsFee {
        /**
         * 押金id
         */
        private Integer feeId;
        /**
         * 费用类型
         */
        private Integer feeType;
        /**
         * 费用值
         */
        private Long feeValue;
        /**
         * 解释
         */
        private String explain;
        /**
         * 名称
         */
        private String feeName;
    }

    @Data
    public static class GoodsBasic {
        /**
         * 数据密级S1,id
         */
        private String id;
        /**
         * 数据密级S1,商户id
         */
        private String merchantId;
        /**
         * 数据密级S1,门店id
         */
        private String siteId;
        /**
         * 数据密级S1,商品名称
         */
        private String productName;
        /**
         * 数据密级S1,年款id
         */
        private String vehicleModelId;
        /**
         * 数据密级S1,车系id
         */
        private String vehicleSeriesId;
        /**
         * 数据密级S1,车系id
         */
        private String vehicleBrandId;
        /**
         *  数据密级S1,牌照类型：0 普通牌照、1 京牌、2 非京牌、3 沪牌、4 非沪牌、5 深牌、6 非深牌、7 粤A牌、8 非粤A牌、9 浙A牌
         */
        private Integer licenseTag;
        /**
         * 数据密级S1,状态：0 未启用（下架）、1 启用（上架）、2 待审核、3 审核通过、4 驳回、5 废弃
         */
        private Integer status;
        /**
         * 是否含有放心租标签
         */
        private Boolean assureRent;
        /**
         * 是否含有放心租标签
         */
        private Boolean assureApply;
        /**
         * 数据密级S1,创建人
         */
        private String createdBy;
        /**
         *  商品版本号，默认1，递增1
         */
        private Integer curVersion;
        /**
         *  数据密级S1,创建时间
         */
        private String createTime;
        /**
         * 修改人
         */
        private String updatedBy;
        /**
         * 修改时间
         */
        private String updateTime ;
    }

    @Data
    public static class ServiceGoods {
        /**
         * 增值服务id
         */
        private Long goodId;
        /**
         * goodsServiceId
         */
        private Long goodsServiceId;
        /**
         * 增值服务名称
         */
        private String goodsServiceName;
        /**
         * 增值服务类型
         */
        private Integer goodsServiceType;
        /**
         * ID
         */
        private Integer id;
        /**
         * 服务价格
         */
        private Long servicePrice;
    }

    @Data
    public static class GoodsRatesDiffConfig {
        /**
         * 若是新增 id为空，修改的id需传
         */
        private Integer id;
        /**
         * goodsId
         */
        private Long goodsId;
        /**
         * 数据密级S1,状态保留1启用
         */
        private Integer status;
        /**
         * 数据密级S1,收否双向
         */
        private Integer twoWay;
        /**
         * 数据密级S1,取车城市
         */
        private String pickCity;
        /**
         * 数据密级S1,还车城市
         */
        private String returnCity;
        /**
         * 数据密级S1,取车城市
         */
        private String pickCityName;
        /**
         * 价格
         */
        private Long price;
        /**
         * 数据密级S1,还车城市
         */
        private String returnCityName;
        /**
         * 数据密级S1,生效时间结束
         */
        private String effectStratTime;
        /**
         * 数据密级S1,生效时间开始
         */
        private String effectEndTime;
        /**
         * 是否过期
         */
        private Boolean expire;
        /**
         *  数据密级S1,创建时间
         */
        private String createTime;
        /**
         * 修改时间
         */
        private String updateTime ;
        /**
         * 键
         */
        private String key ;
    }
}
