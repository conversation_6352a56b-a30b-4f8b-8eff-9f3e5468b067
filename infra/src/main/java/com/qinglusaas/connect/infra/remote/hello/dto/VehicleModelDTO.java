package com.qinglusaas.connect.infra.remote.hello.dto;

import lombok.Data;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2023/10/14, Saturday
 **/
@Data
public class VehicleModelDTO {

    /**
     * 哈啰车型年款code
     */
    private String vehicleModelCode;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 车系名称
     */
    private String seriesName;
    /**
     * 车型年款名称
     */
    private String modelName;
    /**
     * 车型年份
     */
    private Integer modelYear;
    /**
     * 排量 Y
     */
    private String displacement;
    /**
     * 排挡类型
     * 1-自动
     * 2-手动
     * 3-其他
     */
    private Integer transmissionType;

    /**
     * 燃油类型
     * 1-汽油
     * 2-柴油
     * 3-混动
     * 4-纯电
     * 5-其他
     * 6-氢燃料电池
     * 7-增程式
     * 8-插电式混合动力
     * 9-汽油+48V轻混动系统
     */
    private Integer fuelType;
    /**
     * 座位数
     */
    private Integer seatNum;
    /**
     * 车门数
     */
    private Integer doorNum;


}
