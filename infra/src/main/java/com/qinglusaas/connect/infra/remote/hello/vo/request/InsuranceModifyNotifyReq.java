package com.qinglusaas.connect.infra.remote.hello.vo.request;

import com.qinglusaas.connect.infra.remote.hello.dto.ServiceGoodsDesc;
import com.qinglusaas.connect.infra.remote.hello.dto.ServiceGoodsPrice;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class InsuranceModifyNotifyReq extends BaseReq {
    /**
     * ID
     */
    private Long id;
    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 渠道saas code
     */
    private String channelSaasCode;

    /**
     * 增值服务商品名称
     */
    private String goodsServiceName;

//    /**
//     * 增值服务商品类型
//     */
//    private Integer goodsServiceType;

    /**
     * 增值服务描述
     */
    private ServiceGoodsDesc serviceGoodsDesc;

    /**
     * 增值服务零散小时费率
     */
    private ServiceGoodsPrice serviceGoodsPrice;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 关联的商品ids
     */
    private List<Integer> goodsIds;

}
