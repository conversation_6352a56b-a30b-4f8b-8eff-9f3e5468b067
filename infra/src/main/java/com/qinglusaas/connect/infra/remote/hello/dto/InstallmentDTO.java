package com.qinglusaas.connect.infra.remote.hello.dto;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InstallmentDTO {

    /**
     * 开始
     */
    private String start;
    /**
     * 结束
     */
    private String end;

    /**
     * 金额
     */
    private String amount;



    public static List<InstallmentDTO> ableOne() {
        InstallmentDTO installment = new InstallmentDTO();
        installment.setStart("2025-01-01");
        installment.setEnd("2099-01-01");
        installment.setAmount("1");
        return Collections.singletonList(installment);
    }



    public static List<InstallmentDTO> getByVehicleStatus(boolean sable) {
        InstallmentDTO installment = new InstallmentDTO();
        if (sable) {
            installment.setStart("2025-01-01");
            installment.setEnd("2099-01-01");
            installment.setAmount("1");
        } else {
            installment.setStart("2024-01-01");
            installment.setEnd("2025-01-01");
            installment.setAmount("1");
        }
        return Collections.singletonList(installment);
    }

}
