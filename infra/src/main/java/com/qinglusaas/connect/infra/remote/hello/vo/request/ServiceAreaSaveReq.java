package com.qinglusaas.connect.infra.remote.hello.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ServiceAreaSaveReq extends BaseReq{

    /** 用户是否支持自行到店取车标志，0-不支持，1-支持 */
    private Integer userPrCar;

    /** 服务区域列表 */
    private List<ServiceArea> serviceAreaList;

    /** 门店ID */
    private Long siteId;

    /** 商户ID */
    private String merchantId;

    /**
     * 服务区域类
     */
    @Data
    public static class ServiceArea {

        /** 服务类型，1-取车，2-还车 */
        private Integer serviceType;

        /** 区域名称 */
        private String areaName;

        /** 是否支持上门取车服务，0-否，1-是 */
        private Integer isDoorToDoorService;

        /** 服务费用 */
        private Integer serviceFee;

        /** 提前预定时间 */
        private Integer scheduledTime;

        /** 提前预定时间类型 0是分钟，1小时 */
        private Integer scheduledTimeType;

        /** 服务时间起始 */
        private String serviceTimeStart;

        /** 服务时间结束 */
        private String serviceTimeEnd;

        /** 是否提供免费接送服务，0-否，1-是 */
        private Integer isPickupService;

        /** 区域类型，1-多边形区域，2-圆形区域 */
        private Integer areaType;

        /** 多边形区域顶点集合 */
        private List<PolygonPoint> polygonPointList;

        /** 圆形区域点 */
        private CirclePoint circlePoint;

        /** 服务区域半径 */
        private Integer circleRadius;

        /** 门店ID */
        private Long siteId;

        /** 服务区域ID（为空则为新条目） */
        private String id;

        /** saasId,不进行json序列化 */
        @JsonIgnore
        private Long myId;

        // Copy constructor
        public ServiceArea(ServiceArea other) {
            this.serviceType = other.serviceType;
            this.areaName = other.areaName;
            this.isDoorToDoorService = other.isDoorToDoorService;
            this.serviceFee = other.serviceFee;
            this.scheduledTime = other.scheduledTime;
            this.scheduledTimeType = other.scheduledTimeType;
            this.serviceTimeStart = other.serviceTimeStart;
            this.serviceTimeEnd = other.serviceTimeEnd;
            this.isPickupService = other.isPickupService;
            this.areaType = other.areaType;
            this.myId = other.myId;

            if (other.polygonPointList != null) {
                this.polygonPointList = new ArrayList<>(other.polygonPointList);
            }
            if (other.circlePoint != null) {
                this.circlePoint = new CirclePoint(other.circlePoint);  // Assuming CirclePoint has a similar copy constructor
            }
            this.circleRadius = other.circleRadius;
            this.siteId = other.siteId;
            this.id = other.id;
        }

        public ServiceArea() {
        }

        /**
         * 多边形坐标点类
         */
        @Data
        public static class PolygonPoint {

            /** 经度 */
            private Double longitude;

            /** 纬度 */
            private Double latitude;
        }

        /**
         * 圆形坐标点类
         */
        @Data
        public static class CirclePoint {

            /** 经度 */
            private Double longitude;

            /** 纬度 */
            private Double latitude;

            public CirclePoint() {
            }

            public CirclePoint(CirclePoint other) {
                this.longitude = other.longitude;
                this.latitude = other.latitude;
            }
        }
    }
}
