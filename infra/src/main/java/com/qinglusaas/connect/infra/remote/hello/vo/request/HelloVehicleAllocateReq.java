package com.qinglusaas.connect.infra.remote.hello.vo.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HelloVehicleAllocateReq extends BaseReq {


    private String merchantId;

    private List<String> carIds;

    /**
     *
     */
    private Integer status;

    private String goodsId;

    private String siteId;

    private Boolean confirmAllocate;

}
