package com.qinglusaas.connect.infra.remote.hello.vo.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 车辆库存占用查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CarOccupyInventoryQueryReq extends BaseReq {
    /**
     * 车辆ID列表
     */
    private List<String> carIdList;
    
    /**
     * 开始时间
     * 格式: yyyy-MM-dd
     */
    private String startTime;
    
    /**
     * 结束时间
     * 格式: yyyy-MM-dd
     */
    private String endTime;
    
    /**
     * 商户ID
     */
    private String merchantId;
} 