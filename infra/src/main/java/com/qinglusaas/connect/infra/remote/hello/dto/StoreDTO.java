package com.qinglusaas.connect.infra.remote.hello.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> robintse
 * @mailto : <EMAIL>
 * @created : 2022/11/8, Tuesday
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StoreDTO {

    /**
     * 门店编号
     */
    String storeCode;
    /**
     * 门店名称
     */
    String storeName;

    /**
     * 门店类型 1:实体门店, 2:服务网点
     */
    Integer storeType;

    /**
     * 城市编号(电话区号) 城市编号(电话区号)
     */
    String cityCode;

    /**
     * 夜间时段开始时间格式 HH:mm 如 21:30
     */
    String nightFeeStartTime;

    /**
     * 夜间时段结束时间格式 HH:mm 如 07:30
     */
    String nightFeeEndTime;

    /**
     * 门店地址
     */
    String address;

    /**
     * 门店联系电话
     */
    List<String> telephone;
    /**
     * 门店开始营业时间 格式 HH:mm 如 08:30
     */
    String openingTime;

    /**
     * 门店结束营业时间 格式 HH:mm 如 23:30
     */
    String closingTime;

    /**
     * 门店坐标经度(高德坐标GCJ，小数点后6位)
     */
    String longitude;

    /**
     * 门店坐标纬度(高德坐标GCJ，小数点后6位)
     */
    String latitude;

    /**
     * 最小提前预定小时数 0、1、2、3 等
     */

    Integer aheadHour;

    /**
     * 本店车辆是否允许跨城市异地还车,Y:允许,N: 不允许
     */
    String differCityType;

    /**
     * 门店状态 1:启用，2:禁用
     */
    Integer status;

    /**
     * 服务点归属门店
     */
    String parentStoreCode;

}
